(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-communication-userInfo"],{"0030":function(e,t,n){"use strict";n.r(t);var i=n("3ddd"),r=n("8dbf");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);var u=n("f0c5"),a=Object(u["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=a.exports},"07b5":function(e,t,n){"use strict";n.r(t);var i=n("cb1b"),r=n("1889");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("ce2c");var u=n("f0c5"),a=Object(u["a"])(r["default"],i["b"],i["c"],!1,null,"64c4c430",null,!1,i["a"],void 0);t["default"]=a.exports},1889:function(e,t,n){"use strict";n.r(t);var i=n("9bfc"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a},"1ad5":function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("3d6e")),o={getUserListByKeyword:function(e){return(0,r.default)({url:"/app/user/getUserListByKeyword",method:"get",params:e})},getUserInfo:function(e){return(0,r.default)({url:"/app/user/getUserInfo",method:"get",params:e})}};t.default=o},2026:function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("a9e3");var i={props:{value:{type:[String,Number],default:uni.$u.props.input.value},type:{type:String,default:uni.$u.props.input.type},fixed:{type:Boolean,default:uni.$u.props.input.fixed},disabled:{type:Boolean,default:uni.$u.props.input.disabled},disabledColor:{type:String,default:uni.$u.props.input.disabledColor},clearable:{type:Boolean,default:uni.$u.props.input.clearable},password:{type:Boolean,default:uni.$u.props.input.password},maxlength:{type:[String,Number],default:uni.$u.props.input.maxlength},placeholder:{type:String,default:uni.$u.props.input.placeholder},placeholderClass:{type:String,default:uni.$u.props.input.placeholderClass},placeholderStyle:{type:[String,Object],default:uni.$u.props.input.placeholderStyle},showWordLimit:{type:Boolean,default:uni.$u.props.input.showWordLimit},confirmType:{type:String,default:uni.$u.props.input.confirmType},confirmHold:{type:Boolean,default:uni.$u.props.input.confirmHold},holdKeyboard:{type:Boolean,default:uni.$u.props.input.holdKeyboard},focus:{type:Boolean,default:uni.$u.props.input.focus},autoBlur:{type:Boolean,default:uni.$u.props.input.autoBlur},disableDefaultPadding:{type:Boolean,default:uni.$u.props.input.disableDefaultPadding},cursor:{type:[String,Number],default:uni.$u.props.input.cursor},cursorSpacing:{type:[String,Number],default:uni.$u.props.input.cursorSpacing},selectionStart:{type:[String,Number],default:uni.$u.props.input.selectionStart},selectionEnd:{type:[String,Number],default:uni.$u.props.input.selectionEnd},adjustPosition:{type:Boolean,default:uni.$u.props.input.adjustPosition},inputAlign:{type:String,default:uni.$u.props.input.inputAlign},fontSize:{type:[String,Number],default:uni.$u.props.input.fontSize},color:{type:String,default:uni.$u.props.input.color},prefixIcon:{type:String,default:uni.$u.props.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:uni.$u.props.input.prefixIconStyle},suffixIcon:{type:String,default:uni.$u.props.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:uni.$u.props.input.suffixIconStyle},border:{type:String,default:uni.$u.props.input.border},readonly:{type:Boolean,default:uni.$u.props.input.readonly},shape:{type:String,default:uni.$u.props.input.shape},formatter:{type:[Function,null],default:uni.$u.props.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}};t.default=i},"3ddd":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvInput",{attrs:{value:e.value,type:e.type,fixed:e.fixed,disabled:e.disabled,disabledColor:e.disabledColor,clearable:e.clearable,password:e.password,maxlength:e.maxlength,placeholder:e.placeholder,placeholderClass:e.placeholderClass,placeholderStyle:e.placeholderStyle,showWordLimit:e.showWordLimit,confirmType:e.confirmType,confirmHold:e.confirmHold,holdKeyboard:e.holdKeyboard,focus:e.focus,autoBlur:e.autoBlur,disableDefaultPadding:e.disableDefaultPadding,cursor:e.cursor,cursorSpacing:e.cursorSpacing,selectionStart:e.selectionStart,selectionEnd:e.selectionEnd,adjustPosition:e.adjustPosition,inputAlign:e.inputAlign,fontSize:e.fontSize,color:e.color,prefixIcon:e.prefixIcon,suffixIcon:e.suffixIcon,suffixIconStyle:e.suffixIconStyle,prefixIconStyle:e.prefixIconStyle,border:e.border,readonly:e.readonly,shape:e.shape,customStyle:e.customStyle,formatter:e.formatter,ignoreCompositionEvent:e.ignoreCompositionEvent},on:{focus:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("focus")},blur:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("blur",t)}.apply(void 0,arguments)},keyboardheightchange:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("keyboardheightchange")},change:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("change",t)}.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("input",t)}.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),function(t){return e.$emit("confirm",t)}.apply(void 0,arguments)},clear:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("clear")},click:function(t){arguments[0]=t=e.$handleEvent(t),e.$emit("click")}}},[e._t("prefix",null,{slot:"prefix"}),e._t("suffix",null,{slot:"suffix"})],2)},r=[]},4594:function(e,t,n){"use strict";n.r(t);var i=n("6955"),r=n("ba92");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("70e7");var u=n("f0c5"),a=Object(u["a"])(r["default"],i["b"],i["c"],!1,null,"0774a962",null,!1,i["a"],void 0);t["default"]=a.exports},6955:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uIcon:n("3ccb").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-input",class:e.inputClass,style:[e.wrapperStyle]},[n("v-uni-view",{staticClass:"u-input__content"},[e.prefixIcon||e.$slots.prefix?n("v-uni-view",{staticClass:"u-input__content__prefix-icon"},[e._t("prefix",[n("u-icon",{attrs:{name:e.prefixIcon,size:"18",customStyle:e.prefixIconStyle}})])],2):e._e(),n("v-uni-view",{staticClass:"u-input__content__field-wrapper",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[n("v-uni-input",{staticClass:"u-input__content__field-wrapper__field",style:[e.inputStyle],attrs:{type:e.type,focus:e.focus,cursor:e.cursor,value:e.innerValue,"auto-blur":e.autoBlur,disabled:e.disabled||e.readonly,maxlength:e.maxlength,placeholder:e.placeholder,"placeholder-style":e.placeholderStyle,"placeholder-class":e.placeholderClass,"confirm-type":e.confirmType,"confirm-hold":e.confirmHold,"hold-keyboard":e.holdKeyboard,"cursor-spacing":e.cursorSpacing,"adjust-position":e.adjustPosition,"selection-end":e.selectionEnd,"selection-start":e.selectionStart,password:e.password||"password"===e.type||void 0,ignoreCompositionEvent:e.ignoreCompositionEvent},on:{input:function(t){arguments[0]=t=e.$handleEvent(t),e.onInput.apply(void 0,arguments)},blur:function(t){arguments[0]=t=e.$handleEvent(t),e.onBlur.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.onFocus.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.onConfirm.apply(void 0,arguments)},keyboardheightchange:function(t){arguments[0]=t=e.$handleEvent(t),e.onkeyboardheightchange.apply(void 0,arguments)}}})],1),e.isShowClear?n("v-uni-view",{staticClass:"u-input__content__clear",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.onClear.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}})],1):e._e(),e.suffixIcon||e.$slots.suffix?n("v-uni-view",{staticClass:"u-input__content__subfix-icon"},[e._t("suffix",[n("u-icon",{attrs:{name:e.suffixIcon,size:"18",customStyle:e.suffixIconStyle}})])],2):e._e()],1)],1)},o=[]},"70e7":function(e,t,n){"use strict";var i=n("c703"),r=n.n(i);r.a},"8dbf":function(e,t,n){"use strict";n.r(t);var i=n("b147"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a},"909d3":function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("6221")),o=i(n("7340")),u={name:"u--form",mixins:[uni.$u.mpMixin,o.default,uni.$u.mixin],components:{uvForm:r.default},created:function(){this.children=[]},methods:{setRules:function(e){this.$refs.uForm.setRules(e)},validate:function(){return this.$refs.uForm.validate()},validateField:function(e,t){return this.$refs.uForm.validateField(e,t)},resetFields:function(){return this.$refs.uForm.resetFields()},clearValidate:function(e){return this.$refs.uForm.clearValidate(e)},setMpData:function(){this.$refs.uForm.children=this.children}}};t.default=u},"9bfc":function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("1ad5")),o={data:function(){return{isDisabled:!0,userId:null,userInfo:null}},onLoad:function(e){var t=e.userId;this.getUserInfo(t)},methods:{getUserInfo:function(e){var t=this;r.default.getUserInfo({userId:e}).then((function(e){200==e.code&&(t.userInfo=e.data)}))},getSexName:function(e){return"1"===e?"男":"2"===e?"女":""}}};t.default=o},ae47:function(e,t,n){var i=n("b08c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=n("4f06").default;r("d73709c6",i,!0,{sourceMap:!1,shadowMode:!1})},aec9:function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-0774a962], uni-scroll-view[data-v-0774a962], uni-swiper-item[data-v-0774a962]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-input[data-v-0774a962]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;flex:1}.u-input--radius[data-v-0774a962], .u-input--square[data-v-0774a962]{border-radius:4px}.u-input--no-radius[data-v-0774a962]{border-radius:0}.u-input--circle[data-v-0774a962]{border-radius:100px}.u-input__content[data-v-0774a962]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.u-input__content__field-wrapper[data-v-0774a962]{position:relative;display:flex;flex-direction:row;margin:0;flex:1}.u-input__content__field-wrapper__field[data-v-0774a962]{line-height:26px;text-align:left;color:#303133;height:24px;font-size:15px;flex:1}.u-input__content__clear[data-v-0774a962]{width:20px;height:20px;border-radius:100px;background-color:#c6c7cb;display:flex;flex-direction:row;align-items:center;justify-content:center;-webkit-transform:scale(.82);transform:scale(.82);margin-left:4px}.u-input__content__subfix-icon[data-v-0774a962]{margin-left:4px}.u-input__content__prefix-icon[data-v-0774a962]{margin-right:4px}',""]),e.exports=t},b00d:function(e,t,n){"use strict";n.r(t);var i=n("bd8e"),r=n("f1e9");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);var u=n("f0c5"),a=Object(u["a"])(r["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=a.exports},b08c:function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,"uni-page-body[data-v-64c4c430]{background-color:#f8f8f8;width:100%;height:100%}body.?%PAGE?%[data-v-64c4c430]{background-color:#f8f8f8}.content[data-v-64c4c430]{width:90%;height:100%;padding-left:%?20?%;padding-right:%?10?%}",""]),e.exports=t},b147:function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("4594")),o=i(n("2026")),u={name:"u--input",mixins:[uni.$u.mpMixin,o.default,uni.$u.mixin],components:{uvInput:r.default}};t.default=u},ba92:function(e,t,n){"use strict";n.r(t);var i=n("d957"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a},bd8e:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("uvForm",{ref:"uForm",attrs:{model:e.model,rules:e.rules,errorType:e.errorType,borderBottom:e.borderBottom,labelPosition:e.labelPosition,labelWidth:e.labelWidth,labelAlign:e.labelAlign,labelStyle:e.labelStyle,customStyle:e.customStyle}},[e._t("default")],2)},r=[]},c703:function(e,t,n){var i=n("aec9");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var r=n("4f06").default;r("140a89fa",i,!0,{sourceMap:!1,shadowMode:!1})},cb1b:function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={"u-Form":n("b00d").default,uFormItem:n("a4a7").default,"u-Input":n("0030").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"content"},[n("u--form",{ref:"uForm",attrs:{labelPosition:"left"}},[n("u-form-item",{attrs:{label:"姓名",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.userName,callback:function(t){e.$set(e.userInfo,"userName",t)},expression:"userInfo.userName"}})],1),n("u-form-item",{attrs:{label:"性别",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{value:e.getSexName(e.userInfo.sex),border:"none",disabled:e.isDisabled}})],1),n("u-form-item",{attrs:{label:"部门",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.deptNames,callback:function(t){e.$set(e.userInfo,"deptNames",t)},expression:"userInfo.deptNames"}})],1),n("u-form-item",{attrs:{label:"部门链",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.deptChains,callback:function(t){e.$set(e.userInfo,"deptChains",t)},expression:"userInfo.deptChains"}})],1),n("u-form-item",{attrs:{label:"联系电话",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.phonenumber,callback:function(t){e.$set(e.userInfo,"phonenumber",t)},expression:"userInfo.phonenumber"}})],1),n("u-form-item",{attrs:{label:"主数据工号",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.mainJobNo,callback:function(t){e.$set(e.userInfo,"mainJobNo",t)},expression:"userInfo.mainJobNo"}})],1),n("u-form-item",{attrs:{label:"企业微信工号",borderBottom:!0,labelWidth:"200rpx"}},[n("u--input",{attrs:{border:"none",disabled:e.isDisabled},model:{value:e.userInfo.qyJobNo,callback:function(t){e.$set(e.userInfo,"qyJobNo",t)},expression:"userInfo.qyJobNo"}})],1)],1)],1)},o=[]},ce2c:function(e,t,n){"use strict";var i=n("ae47"),r=n.n(i);r.a},d957:function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("99af"),n("14d9");var r=i(n("2026")),o={name:"u-input",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:function(e){return e}}},watch:{value:{immediate:!0,handler:function(e,t){this.innerValue=e,!1===this.firstChange&&!1===this.changeFromInner&&this.valueChange(),this.firstChange=!1,this.changeFromInner=!1}}},computed:{isShowClear:function(){var e=this.clearable,t=this.readonly,n=this.focused,i=this.innerValue;return!!e&&!t&&!!n&&""!==i},inputClass:function(){var e=[],t=this.border,n=(this.disabled,this.shape);return"surround"===t&&(e=e.concat(["u-border","u-input--radius"])),e.push("u-input--".concat(n)),"bottom"===t&&(e=e.concat(["u-border-bottom","u-input--no-radius"])),e.join(" ")},wrapperStyle:function(){var e={};return this.disabled&&(e.backgroundColor=this.disabledColor),"none"===this.border?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))},inputStyle:function(){var e={color:this.color,fontSize:uni.$u.addUnit(this.fontSize),textAlign:this.inputAlign};return e}},methods:{setFormatter:function(e){this.innerFormatter=e},onInput:function(e){var t=this,n=e.detail||{},i=n.value,r=void 0===i?"":i,o=this.formatter||this.innerFormatter,u=o(r);this.innerValue=r,this.$nextTick((function(){t.innerValue=u,t.valueChange()}))},onBlur:function(e){var t=this;this.$emit("blur",e.detail.value),uni.$u.sleep(50).then((function(){t.focused=!1})),uni.$u.formValidate(this,"blur")},onFocus:function(e){this.focused=!0,this.$emit("focus")},onConfirm:function(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange:function(){this.$emit("keyboardheightchange")},valueChange:function(){var e=this,t=this.innerValue;this.$nextTick((function(){e.$emit("input",t),e.changeFromInner=!0,e.$emit("change",t),uni.$u.formValidate(e,"change")}))},onClear:function(){var e=this;this.innerValue="",this.$nextTick((function(){e.valueChange(),e.$emit("clear")}))},clickHandler:function(){}}};t.default=o},f1e9:function(e,t,n){"use strict";n.r(t);var i=n("909d3"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=r.a}}]);