<template>
  <view class="x-upload">
    <upload
        ref="upload"
        @on-preview="handlePreview"
        @on-list-change="handleListChange"
        @on-remove="handleRemove"
        @on-success="handleSuccess"
        @on-error="handleError"
        :disabled="disabled"
        :show-progress="false"
        :multiple="false"
        :customBtn="customBtn"
        :showUploadList="showUploadList"
        :width="width"
        :height="height"
        :file-list="list"
        :uploadText="uploadText"
        :limitType="limitType"
        :max-size="maxSize"
        :max-count="maxCount"
        :before-upload="beforeUpload"
        :deletable="deletable"
        :action="action">
      <template v-slot:file="list">
        <slot name="file" :list="list"></slot>
      </template>
      <template v-slot:addBtn>
        <slot name="addBtn"></slot>
      </template>
    </upload>
    <x-pdf ref="xPdf" :url="src"></x-pdf>
    <x-office ref="office" :url="src"></x-office>
  </view>
</template>

<script>

import Upload from './upload';
import XPdf from './x-pdf';
import XOffice from './x-office';

const doPreviewImage = function (url, index) {
  if (!this.previewFullImage) return;
  this.$emit('on-preview', url, this.lists, index);
};
const imgType = ['jpg', 'png', 'gif', 'jpeg', 'bmp', 'tiff', 'psd', 'eps', 'tga']
const imgTypeFun = (url) => {
  for (let i = 0; i < imgType.length; i++) {
    if (url.includes(imgType[i])) {
      return true
    }
  }
  return false;
}
export default {
  name: 'XUpload',
  components: {
    Upload,
    XOffice,
    XPdf
  },
  props: {
    // 是否显示组件自带的图片预览功能
    showUploadList: {
      type: Boolean,
      default: true
    },
    // 是否通过slot自定义传入选择图标的按钮
    customBtn: {
      type: Boolean,
      default: false
    },
    // 是否启用
    disabled: {
      type: Boolean,
      default: false
    },
    // 上传区域的提示文字
    uploadText: {
      type: String,
      default: ''
    },
    // 内部预览图片区域和选择图片按钮的区域宽度
    width: {
      type: [String, Number],
      default: 175
    },
    // 内部预览图片区域和选择图片按钮的区域高度
    height: {
      type: [String, Number],
      default: 175
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 3
    },
    // 文件大小限制，单位为byte
    maxSize: {
      type: String,
      default: '10485760'
    },
    // 后端地址
    action: {
      type: String,
      default: ''
    },
    // 显示已上传的文件列表
    fileList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 允许上传的图片后缀
    limitType: {
      type: Array,
      default: () => {
        return imgType;
      }
    },
    // 是否展示删除按钮
    deletable: {
      type: Boolean,
      default: true
    },
    // 上传前的钩子，每个文件上传前都会执行
    beforeUpload: {
      type: Function
    },
    isBindModel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      src: '',
      list: []
    };
  },
  watch: {
    fileList: {
      immediate: true,
      handler(v) {
        this.list = v.map((item, index) => {
          let url = (item.filePath || item.url || '').toLowerCase();
          if (/.pdf$/i.test(url)) {
            url = '//oss.newpearl.com/newpearl/img/2021-03-18/74efcf63e34d4ca98fb27b445e628c83.png?index=' + index;
            return {
              url
            }
          }
          if (/.xls$/i.test(url) || /.xlsx$/i.test(url)) {
            url = '//oss.newpearl.com/newpearl/img/file-img/EXCEL.png?index=' + index;
            return {
              url
            }
          }
          if (/.docx$/i.test(url) || /.doc$/i.test(url)) {
            url = '//oss.newpearl.com/newpearl/img/file-img/WORD.png?index=' + index;
            return {
              url
            }
          }
          if (/.ppt/i.test(url) || /.pptx/i.test(url)) {
            url = '//oss.newpearl.com/newpearl/img/file-img/PPT.png?index=' + index;
            return {
              url
            }
          }
          if (!imgTypeFun(url)) {
            url = '//oss.newpearl.com/newpearl/img/2021-11-02/b445afc62e59482e95f5ea40da3229f9.png?index=' + index;
            return {
              url
            }
          }
          return {
            url
          }
        });
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.upload.doPreviewImage = doPreviewImage;
    });
  },
  methods: {
    handlePreview(file, lists, index) {
      file = file.includes('?') ? file.split('?')[0] : file;
      const imgArr = [
        '//oss.newpearl.com/newpearl/img/file-img/EXCEL.png',
        '//oss.newpearl.com/newpearl/img/file-img/PPT.png',
        '//oss.newpearl.com/newpearl/img/file-img/WORD.png'
      ]
      const delArr = [
        '//oss.newpearl.com/newpearl/img/2021-03-18/74efcf63e34d4ca98fb27b445e628c83.png',
        '//oss.newpearl.com/newpearl/img/2021-11-02/b445afc62e59482e95f5ea40da3229f9.png',
        ...imgArr
      ];
      // 只支持预览pdf
      if (file.indexOf('//oss.newpearl.com/newpearl/img/2021-03-18/74efcf63e34d4ca98fb27b445e628c83.png') !== -1) {
        const fileItem = this.fileList[index]
        this.src = fileItem.filePath || fileItem.url;
        this.$refs.xPdf.visible = true;
        return;
      }
      if (imgArr.includes(file)) {
        console.log('this.fileList[index]', this.fileList[index]);
        const fileItem = this.fileList[index]
        this.src = fileItem.filePath || fileItem.url;
        this.$refs.office.visible = true;
        return;
      }
      if (file.indexOf('//oss.newpearl.com/newpearl/img/2021-11-02/b445afc62e59482e95f5ea40da3229f9.png') !== -1) {
        uni.showToast({
          title: '不支持该类型文件预览',
          icon: 'none'
        });
        return;
      }
      const images = [];
      lists.forEach(item => {
        if (!delArr.includes(item.url)) {
          images.push(item.url);
        }
      });
      uni.previewImage({
        urls: images,
        current: file,
        fail: () => {
          uni.showToast({
            title: '预览图片失败',
            icon: 'none'
          });
        }
      });
    },
    handleListChange(lists, name) {
      lists.forEach((item, index) => {
        if (item.file) {
          if (item.file.type === 'application/pdf') {
            item.url = '//oss.newpearl.com/newpearl/img/2021-03-18/74efcf63e34d4ca98fb27b445e628c83.png?index=' + index;
          }else if (item.file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || item.file.type === 'application/vnd.ms-excel') {
            item.url = '//oss.newpearl.com/newpearl/img/file-img/EXCEL.png?index=' + index;
          } else if (item.file.name.includes('.ppt')) {
            item.url = '//oss.newpearl.com/newpearl/img/file-img/PPT.png?index=' + index;
          } else if (item.file.type === 'application/msword' || item.file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
            item.url = '//oss.newpearl.com/newpearl/img/file-img/WORD.png?index=' + index;
          }else if (!item.file.type.includes('image')) {
            item.url = '//oss.newpearl.com/newpearl/img/2021-11-02/b445afc62e59482e95f5ea40da3229f9.png?index=' + index;
          }
        }
      });
      // this.$emit('update:fileList', lists);
    },
    handleSuccess(data, index, lists, name) {
      // this.fileList.push(data.data);
      // if (this.isBindModel && data.data.fileSuffix !== '.pdf') {
      if (imgTypeFun(data.data.filePath)) {
        lists.splice(index, 1);
      }
      this.$emit('success', data.data, index);
    },
    handleError(res, index, lists, name) {
      // console.log(res, index, lists, name);
      this.showToast('上传失败');
      this.list.splice(index, 1);
      this.$emit('error', res);
    },
    handleRemove(index, lists, name) {
      // lists.splice(index, 1);
      this.$emit('remove', index);
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .docx-wrapper{
  padding: 0!important;
  overflow: auto !important;
}
::v-deep .docx{
  width: 1000px!important;
  overflow: auto !important;
  display: table!important;
  padding: 2em 1em 0!important;
}

::v-deep .uni-scroll-view-content{
  background: #4a4a4a;
}
</style>
