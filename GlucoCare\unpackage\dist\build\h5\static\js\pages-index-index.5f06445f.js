(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index"],{"0eeb":function(t,e,n){var o=n("3718");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var s=n("4f06").default;s("3997c302",o,!0,{sourceMap:!1,shadowMode:!1})},2808:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"container"},[n("v-uni-view",[t._v(t._s(t.status))]),n("v-uni-view",[t._v(t._s(t.url)+"1")]),n("v-uni-view",[t._v(t._s(t.response))])],1)},s=[]},3718:function(t,e,n){var o=n("24fb");e=o(!1),e.push([t.i,".container[data-v-bb385e2e]{padding:20px}",""]),t.exports=e},"5a5f":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e9c4");var o=n("eeb8"),s={data:function(){return{status1:"飞书登录中......",status:"初始化...",response:"",url:""}},onShow:function(){this.url=window.location.href.split("?")[0].split("#")[0],this.initFeishuAuth()},onReady:function(){this.initFeishuAuth()},methods:{initFeishuAuth:function(){var t=this;this.status="动态加载飞书 SDK...",console.log(this.status);var e=document.createElement("script");e.src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.26.js",e.onload=function(){t.status="飞书 SDK 加载完成，等待初始化...",console.log(t.status);var e=setInterval((function(){window.tt&&"function"===typeof window.tt.requestAuthCode?(clearInterval(e),t.status="开始获取授权码...",console.log(t.status),window.tt.requestAuthCode({appId:"cli_a641cb203b78d013",success:function(e){t.status="授权码获取成功，发送至服务器...",console.log(t.status),console.log(e,"--------------getAuthCode succeed"),t.getAuthCode(e.code)},fail:function(e){t.status="获取授权码失败",t.response=JSON.stringify(e),console.log(t.status),console.log("getAuthCode failed, err:",JSON.stringify(e))}})):window.tt&&window.tt.error&&(t.status="飞书 SDK 初始化失败，无法找到 PC Bridge",console.log(t.status),clearInterval(e))}),100)},e.onerror=function(){t.status="飞书 SDK 加载失败",console.log(t.status)},document.head.appendChild(e)},getAuthCode:function(t){var e=this;this.status="正在请求服务器验证授权码...",console.log(this.status),uni.request({url:"http://114.132.51.15:8089/ssoApp/oauth/feishu/meet/callback",method:"GET",data:{code:t},success:function(t){e.response=t;var n=t.data;if(200===n.code||"200"===n.code||200==n.code||"200"==n.code){var s=n.msg;(0,o.setToken)(s),uni.redirectTo({url:"/pages/meet/meet"}),setTimeout((function(){e.$message.success("登录成功")}),500)}else e.status1="请刷新重试或联系管理员！"},fail:function(t){e.status1="请刷新重试或联系管理员！",console.error(e.status,t)}})}}};e.default=s},ac88:function(t,e,n){"use strict";n.r(e);var o=n("2808"),s=n("bced");for(var u in s)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return s[t]}))}(u);n("e9ca");var i=n("f0c5"),a=Object(i["a"])(s["default"],o["b"],o["c"],!1,null,"bb385e2e",null,!1,o["a"],void 0);e["default"]=a.exports},bced:function(t,e,n){"use strict";n.r(e);var o=n("5a5f"),s=n.n(o);for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(u);e["default"]=s.a},e9ca:function(t,e,n){"use strict";var o=n("0eeb"),s=n.n(o);s.a},eeb8:function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.getToken=function(){return uni.getStorageSync("Admin-Token")},e.removeToken=function(){return uni.removeStorageSync("Admin-Token")},e.setToken=function(t){return uni.setStorageSync("Admin-Token",t)}}}]);