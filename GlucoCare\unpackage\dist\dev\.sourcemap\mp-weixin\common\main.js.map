{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/App.vue?3e27", "uni-app:///App.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/App.vue?8c46", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/App.vue?80b8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "prototype", "$message", "success", "title", "uni", "showToast", "error", "icon", "loading", "flag", "showLoading", "mask", "hideLoading", "modal", "option", "content", "showModal", "confirm", "cancel", "action", "list", "key", "showActionSheet", "itemList", "map", "item", "tapIndex", "fail", "errMsg", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "console", "onShow", "onHide", "methods", "checkLoginStatus", "url"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AACA;AACA;AAA2C;AAAA;AAN3C;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAM1DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AAGdF,YAAG,CAACG,SAAS,CAACC,QAAQ,GAAG;EACxBC,OAAO,mBAACC,KAAK,EAAE;IACdC,GAAG,CAACC,SAAS,CAAC;MAAEF,KAAK,EAALA;IAAM,CAAC,CAAC;EACzB,CAAC;EACDG,KAAK,iBAACH,KAAK,EAAE;IACZC,GAAG,CAACC,SAAS,CAAC;MACbF,KAAK,EAALA,KAAK;MACLI,IAAI,EAAE;IACP,CAAC,CAAC;EACH,CAAC;EACDC,OAAO,qBAAqB;IAAA,IAApBC,IAAI,uEAAG,IAAI;IAAA,IAAEN,KAAK;IACzBM,IAAI,GAAGL,GAAG,CAACM,WAAW,CAAC;MAAEP,KAAK,EAALA,KAAK;MAAEQ,IAAI,EAAE;IAAK,CAAC,CAAC,GAAGP,GAAG,CAACQ,WAAW,EAAE;EAClE,CAAC;EACDC,KAAK,mBAA0E;IAAA,IAAzEC,MAAM,uEAAG;MAAEX,KAAK,EAAE,EAAE;MAAEY,OAAO,EAAE,EAAE;MAAEb,OAAO,EAAE,mBAAM,CAAC,CAAC;MAAEI,KAAK,EAAE,iBAAM,CAAC;IAAE,CAAC;IAC5E,IAAQH,KAAK,GAA8BW,MAAM,CAAzCX,KAAK;MAAEY,OAAO,GAAqBD,MAAM,CAAlCC,OAAO;MAAEb,QAAO,GAAYY,MAAM,CAAzBZ,OAAO;MAAEI,KAAK,GAAKQ,MAAM,CAAhBR,KAAK;IACtCF,GAAG,CAACY,SAAS,CAAC;MACbb,KAAK,EAALA,KAAK;MACLY,OAAO,EAAPA,OAAO;MACPb,OAAO,EAAE,uBAAyB;QAAA,IAAtBe,OAAO,QAAPA,OAAO;UAAEC,MAAM,QAANA,MAAM;QAC1B,IAAID,OAAO,EAAE;UACZf,QAAO,IAAIA,QAAO,EAAE;QACrB,CAAC,MAAM,IAAIgB,MAAM,EAAE;UAClBZ,KAAK,IAAIA,KAAK,EAAE;QACjB;MACD;IACD,CAAC,CAAC;EACH,CAAC;EACDa,MAAM,oBAOJ;IAAA,IANDL,MAAM,uEAAG;MACRM,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPnB,OAAO,EAAE,mBAAM,CAAC,CAAC;MACjBI,KAAK,EAAE,iBAAM,CAAC;IACf,CAAC;IAED,IAAQc,IAAI,GAA0BN,MAAM,CAApCM,IAAI;MAAEC,GAAG,GAAqBP,MAAM,CAA9BO,GAAG;MAAEnB,SAAO,GAAYY,MAAM,CAAzBZ,OAAO;MAAEI,KAAK,GAAKQ,MAAM,CAAhBR,KAAK;IACjCF,GAAG,CAACkB,eAAe,CAAC;MACnBC,QAAQ,EAAEH,IAAI,CAACI,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAKJ,GAAG,GAAGI,IAAI,CAACJ,GAAG,CAAC,GAAGI,IAAI;MAAA,CAAC,CAAC;MACpDvB,OAAO,EAAE,wBAAkB;QAAA,IAAfwB,QAAQ,SAARA,QAAQ;QACnBxB,SAAO,IAAIA,SAAO,CAACkB,IAAI,CAACM,QAAQ,CAAC,CAAC;MACnC,CAAC;MACDC,IAAI,EAAE,qBAAgB;QAAA,IAAbC,MAAM,SAANA,MAAM;QACdtB,KAAK,IAAIA,KAAK,CAACsB,MAAM,CAAC;MACvB;IACD,CAAC,CAAC;EACH;AACD,CAAC;AAGD/B,YAAG,CAACgC,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIpC,YAAG,mBACdkC,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACjEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAopB,CAAgB,qpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACCxqB;AAAA,eAEA;EACAC;IACAC;IACA;EACA;;EACAC;IACAD;EACA;EACAE;IACAF;EACA;EACAG;IACA;IACAC;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MAEA;MACA;;MAEA;MACA;QACApC;UACAqC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAA2uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACA/vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nimport uView from '@/node_modules/uview-ui'\r\nVue.use(uView)\r\n\r\n\r\nVue.prototype.$message = {\r\n\tsuccess(title) {\r\n\t\tuni.showToast({ title })\r\n\t},\r\n\terror(title) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle,\r\n\t\t\ticon: 'error',\r\n\t\t})\r\n\t},\r\n\tloading(flag = true, title) {\r\n\t\tflag ? uni.showLoading({ title, mask: true }) : uni.hideLoading()\r\n\t},\r\n\tmodal(option = { title: \"\", content: \"\", success: () => {}, error: () => {} }) {\r\n\t\tconst { title, content, success, error } = option\r\n\t\tuni.showModal({\r\n\t\t\ttitle,\r\n\t\t\tcontent,\r\n\t\t\tsuccess: ({ confirm, cancel }) => {\r\n\t\t\t\tif (confirm) {\r\n\t\t\t\t\tsuccess && success()\r\n\t\t\t\t} else if (cancel) {\r\n\t\t\t\t\terror && error()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t})\r\n\t},\r\n\taction(\r\n\t\toption = {\r\n\t\t\tlist: [],\r\n\t\t\tkey: \"\",\r\n\t\t\tsuccess: () => {},\r\n\t\t\terror: () => {},\r\n\t\t},\r\n\t) {\r\n\t\tconst { list, key, success, error } = option\r\n\t\tuni.showActionSheet({\r\n\t\t\titemList: list.map(item => (key ? item[key] : item)),\r\n\t\t\tsuccess: ({ tapIndex }) => {\r\n\t\t\t\tsuccess && success(list[tapIndex])\r\n\t\t\t},\r\n\t\t\tfail: ({ errMsg }) => {\r\n\t\t\t\terror && error(errMsg)\r\n\t\t\t},\r\n\t\t})\r\n\t},\r\n}\r\n\r\n\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport { getToken, getUserInfo } from '@/utils/token.js'\r\n\t\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t\t// this.checkLoginStatus()\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 检查登录状态\r\n\t\t\tcheckLoginStatus() {\r\n\t\t\t\t// 获取当前页面路径\r\n\t\t\t\tconst pages = getCurrentPages()\r\n\t\t\t\tconst currentPage = pages[pages.length - 1]\r\n\t\t\t\tconst currentRoute = currentPage ? currentPage.route : ''\r\n\t\t\t\t\r\n\t\t\t\t// 如果是登录或注册页面，不进行检查\r\n\t\t\t\tif (currentRoute.includes('login') || currentRoute.includes('register')) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tconst token = getToken()\r\n\t\t\t\tconst userInfo = getUserInfo()\r\n\t\t\t\t\r\n\t\t\t\t// 如果没有token或用户信息，跳转到登录页面\r\n\t\t\t\tif (!token || !userInfo) {\r\n\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import \"@/node_modules/uview-ui/index.scss\";\r\n</style>", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973584631\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}