<template>
	<view class="edream-calendar">
		<view class="calendar-header">
			<view class="flex align-items-center">
				{{calYear}} 
				<template v-if="[0,1,2].includes(mode)">
					<text class="line">|</text>
					<text>{{calMonth}}</text>
				</template>
			</view>
			<view class="calendar-switch" :style="{
				width: calendarSwitchWidth
			}">
				<view class="switch-active" :class="{'mode-level-2': mode === 2, 'mode-level-3': mode === 3}"></view>
<!-- 				<view class="switch-item day" @click="onChangeMode(0)">
					<text>日</text>
				</view> -->
				<view class="switch-item week" @click="onChangeMode(2)" v-if="showWeek">
					<text>周</text>
				</view>
				<view class="switch-item month" @click="onChangeMode(3)" v-if="showMonth">
					<text>月</text>
				</view>
			</view>
		</view>
					<!-- 日|日历模式 -->
		<!-- <template v-if="[0, 1].includes(mode)">
			<view class="calendar-weeks">
				<view class="calendar-week" v-for="(week, index) in weeks" :key="index">
					{{week}}
				</view>
			</view>
			<view class="calendar-content">
				<swiper class="calendar-swiper" :style="{
					 width: '100%',
					 height: sheight
				 }" :indicator-dots="false" :autoplay="false" :duration="duration" :current="current" @change="onChangeSwiper" :circular="true">
					<swiper-item class="calendar-swiper-item" v-for="(parentItem, parentIndex) in swiper" :key="parentIndex">
						<view class="calendar-dates-wrapper">
							<template v-if="parentIndex === current">
								<view class="calendar-day" v-for="(item,index) in dates" :key="index"
								:class="{
									'day-perMonth': item.isPreMonth
								}" @click="onSelectItem(item)">
									<view
										class="date"
										:class="[
											item.isToday ? todayClass : '',
											item.fullDate === selectedDate.startDate ? checkedClass : ''
											]"
									>
										{{item.txt}}
									</view>
								</view>
							</template>
							<template v-else>
								<template v-if="current - parentIndex === 1 || current - parentIndex ===-2">
									<view class="calendar-day" v-for="(item,index) in preDates" :key="index">
										<view
											class="date"
											:class="[
												item.isToday ? todayClass : ''
											]"
										>
											{{item.txt}}
										</view>
									</view>
								</template>
								<template v-else>
									<view class="calendar-day" v-for="(item,index) in nextDates" :key="index">
										<view
											class="date"
											:class="[
												item.isToday ? todayClass : ''
											]"
										>
											{{item.txt}}
										</view>
									</view>
								</template>
							</template>
						</view>				
					</swiper-item>			
				</swiper>
				<view class="calendar-month-bg" v-show="mode === 1">{{calMonth}}</view>
				<view class="mode-change" @click="onChangeMode(mode === 0 ? 1 : 0)">
					<view class="arrow-line"></view>
	  			<image src="../../static/arrow.png" class="arrow-icon" :class="{active: mode === 1}"></image>
				</view>
			</view>
		</template> -->
		<template v-if="[2, 3].includes(mode)">
			<!-- 周|月模式 -->
			<view class="calendar-content">
				<swiper class="calendar-swiper calendar-week-swiper" :indicator-dots="false" :autoplay="false" :duration="duration" :current="current" @change="onChangeSwiper" :circular="true">
					<swiper-item class="calendar-swiper-item" v-for="(parentItem, parentIndex) in swiper" :key="parentIndex">
						<template v-if="parentIndex === current">
							<view class="range-date-wrapper" :class="{'range-week-wrapper': mode === 2}">
								<view class="range-date-item" 
									v-for="(item, index) in dates" 
									:key="index" 
									:class="[
										selectedDate.startDate === item[0].fullDate && selectedDate.endDate === item[item.length - 1].fullDate ? checkedClass : '',
										item.some(subItem => subItem.isToday) || item.some(subItem => subItem.txt === todayInMonth)? todayClass : '',
										mode === 2 ? 'week-item' : '',
										mode === 3 ? 'month-item' : '',
									]"
									@click="onSelectItem(item)"
								>
									<template v-if="mode === 2">
										<text class="range-date-index">第{{index + 1}}周</text>
										<view class="range-date-days">
											 {{item[0].txt}}至{{item[item.length - 1].txt}}
										</view>
									</template>
									<template v-else>
										<text class="range-date-index">{{item[0].txt}}</text>
									</template>
								</view>
							</view>
						</template>
						<template v-else>
							<template v-if="current - parentIndex === 1 || current - parentIndex ===-2">
								<view class="range-date-wrapper">
									<view class="range-date-item" v-for="(item, index) in preDates" :key="index" :class="{'month-item': mode === 3}">
										<template v-if="mode === 2">
											<text class="range-date-index">第{{index + 1}}周</text>
											<view class="range-date-days">
												 {{item[0].txt}}至{{item[item.length - 1].txt}}
											</view>
										</template>
										<template v-else>
											<text class="range-date-index">{{item[0].txt}}</text>
										</template>
									</view>
								</view>
							</template>
							<template v-else>
								<view class="range-date-wrapper">
									<view class="range-date-item" v-for="(item, index) in nextDates" :key="index" :class="{'month-item': mode === 3}">
										<template v-if="mode === 2">
											<text class="range-date-index">第{{index + 1}}周</text>
											<view class="range-date-days">
												 {{item[0].txt}}至{{item[item.length - 1].txt}}
											</view>
										</template>
										<template v-else>
											<text class="range-date-index">{{item[0].txt}}</text>
										</template>
									</view>
								</view>
							</template>
						</template>
					</swiper-item>
				</swiper>
			</view>
		</template>
	</view>
</template>

<script>
import dayjs from './dayjs.min.js'
dayjs.locale('zh-cn', {
  weekStart: 1 // 设置周一为一周的开始
})

const isSameOrBefore = (base, target) => {
  return base.isSame(target) || base.isBefore(target)
}
dayjs.extend((_, dayjs, __) => {
  dayjs.prototype.isSameOrBefore = function(target) {
    return isSameOrBefore(this, target)
  }
})

export default {
	props: {
		duration: {
			type: Number,
			default: 500
		},
		showWeek: {
			type: Boolean,
			default: true
		},
		showMonth: {
			type: Boolean,
			default: true
		},
		todayClass: {
			type: String, // 今日的自定义样式class
			default: 'is-today'
		},
		checkedClass: {
			type: String, // 选中日期的样式class
			default: 'is-checked'
		}
	},
	data() {
	 return {
			weeks: ['一', '二', '三', '四', '五', '六', '日'],
			swiper: [0, 1, 2],
			current: 1,
			mode: 2,  // 0:日模式; 1:日历模式, 2:周模式, 3:月模式
			dates: [], // 日|日历|周模式展示日期数据
			preDates: [], // 上一页日期数据
			nextDates: [], // 下一页日期数据
			weekLen: 6,
			today: dayjs(),
			calDate: dayjs(),
			selectedDate: {
				date: dayjs(),
				startDate: dayjs().format('YYYY-MM-DD'),
				endDate: ''
			},
		};
	},
	computed: {
		sheight() {
			if (this.mode === 1) {
				return  `${70 * this.weekLen}rpx`
			} else if ([0, 3].includes(this.mode)){
				return '70rpx'
			} else if (this.mode === 2) {
				return '300rpx'
			}
		},
		calendarSwitchWidth() {
			if (this.showWeek && this.showMonth) {
				return '210rpx'
			} else if (this.showWeek || this.showMonth) {
				return '140rpx'
			} else {
				return '70rpx'
			}
		},
		calYear() {
			return this.calDate.format('YYYY')
		},
		calMonthDay() {
			return this.calDate.format('MM-DD')
		},
		calMonth() {
			return this.calDate.format('MM月')
		},
		todayInMonth() {
			return this.today.format('MM月')
		}
	},
	created() {
		this.dateFormatStr = 'YYYY-MM-DD'
		this.calDate = dayjs()
		this.initDate(this.calDate, this.mode)
	},
	methods: {
		// 获取月
		getMonths(date) {
			const dateFormatStr =  this.dateFormatStr
			let dates = [];
			for (let i = 0; i < 6; i++) {
				const currentMonth = date.add(i, 'month');
			  const startOfMonth = currentMonth.startOf('month');
			  const endOfMonth = currentMonth.endOf('month');
				dates.push([
					this.formatProperty(startOfMonth, startOfMonth, endOfMonth, currentMonth),
					this.formatProperty(endOfMonth, startOfMonth, endOfMonth, currentMonth),
				])
			} 
			// console.log('getMonthsdates', dates)
			return {
				dates,
				weekLen: 1
			} 
		},
		// 获取每周的时间段
		getWeeks(date) {
			let startOfMonth = date.startOf('month');
			let endOfMonth = date.endOf('month');
			let nextMonth = date.add(1, 'month').startOf('month');
		  let dates = [];
			let currentDate = startOfMonth;
			const startDayOfWeek = startOfMonth.day();
			currentDate = currentDate.subtract(startDayOfWeek - 1, 'day');
			for (let week = 0; week < 6; week++) {
				// 过滤最后一周都是下一个月的情况
				if (week === 5 && currentDate.isSame(nextMonth, 'month')) {
					break
				} 
			  const weekRow = [];
				for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
				 weekRow.push(this.formatProperty(currentDate, startOfMonth, endOfMonth, date))
					currentDate = currentDate.add(1, 'day');
			  }
				dates.push(weekRow);
			}
			dates = dates.filter(item => item.length)
			return {
				dates,
				weekLen: dates.length
			}
		},
		// 获取本周日期
		getDate(date, mode) {
			const dateFormatStr = this.dateFormatStr
			let startOfMonth = date.startOf('month');
			let endOfMonth = date.endOf('month');
			let tempWeekStartDay = date.startOf('week');
			let tempWeekEndDay = date.endOf('week');			
			// 本周收起或展开状态
			if (this.mode === 0) { 
				let dates = [];
				// 循环获取每一天的日期
				let currentDay = tempWeekStartDay;
				while (tempWeekStartDay.isSameOrBefore(tempWeekEndDay)) {
					dates.push(this.formatProperty(tempWeekStartDay, startOfMonth, endOfMonth, date))
					tempWeekStartDay = tempWeekStartDay.add(1, 'day');
				}
				return {
					dates,
					weekLen: 1
				};
			} else if(this.mode === 1) {
				let {dates, weekLen} = this.getWeeks(date);
				dates = dates.flat()
				return {
					dates,
					weekLen
				};
			}
		},
		// 初始化日期
		initDate(date, mode = this.mode) {
		  this.initCurDate(date, mode)
			this.initPreDate(date, mode)
			this.initNextDate(date, mode)
		},
		// 初始化当前页日期
		initCurDate(date, mode) {
			const { dates, weekLen } = [
				this.getDate(date),
				this.getDate(date),
				this.getWeeks(date),
				this.getMonths(date)
			][mode]
			this.dates = dates
			// console.log('dates', dates)
			this.weekLen = weekLen
		},
		// 初始化上一页日期
		initPreDate(date, mode) {
			let _date = [
				date.subtract(7, 'day'),
				date.subtract(1, 'month'),
				date.subtract(1, 'month'),
				date.subtract(6, 'month')
			][mode]
			const { dates } = [
				this.getDate(_date),
				this.getDate(_date),
				this.getWeeks(_date),
				this.getMonths(_date)
			][mode]
			this.preDates = dates
		},
		// 初始化下一页日期
		initNextDate(date, mode) {
			let _date = [
				date.add(7, 'day'),
				date.add(1, 'month'),
				date.add(1, 'month'),
				date.add(6, 'month')
			][mode]
			const { dates } = [
				this.getDate(_date),
				this.getDate(_date),
				this.getWeeks(_date),
				this.getMonths(_date)
			][mode]
			this.nextDates = dates
		},
		// 轮播切换
		onChangeSwiper(e) {
			const pre = this.current;
			const current = e.target.current;
			this.current = current;
			if (current - pre === 1 || current - pre === -2) {
				this.selectedDate.date = this.calDate = [
					this.calDate.add(7, 'day'),
					this.calDate.add(1, 'month'),
					this.calDate.add(1, 'month'),
					this.calDate.add(6, 'month')
				][this.mode]
				this.initDate(this.calDate, this.mode);
			} else {
				this.selectedDate.date = this.calDate = [
					this.calDate.subtract(7, 'day'),
					this.calDate.subtract(1, 'month'),
					this.calDate.subtract(1, 'month'),
					this.calDate.subtract(6, 'month')
				][this.mode]
				this.initDate(this.calDate, this.mode);
			}
		},
		// 显示属性格式化
		formatProperty(date, startOfMonth, endOfMonth, today) {
			const dateFormatStr =  this.dateFormatStr
			return {
				fullDate: date.format(dateFormatStr), // 日|日历日期
				isToday: date.isSame(this.today, 'day'), // 是否是今天
				isPreMonth: !(date.isSame(startOfMonth, 'month') && date.isSame(endOfMonth, 'month')), // 是否是上个月
				txt: [
					date.format('DD'),   
					date.format('DD'),
					date.format('MM-DD'),
					date.format('MM月')
				][this.mode]
			}
		},
		onChangeMode(mode) {
			this.mode = mode;
			this.initDate(this.calDate, this.mode);
		},
		onSelectItem(item) {
			if (Array.isArray(item)) {
				this.selectedDate.startDate = item[0].fullDate
				this.selectedDate.endDate = item[item.length - 1].fullDate
			} else {
				this.selectedDate.startDate = item.fullDate
				this.selectedDate.endDate = item.fullDate
			}
			this.$emit('calSelected', {startDate: this.selectedDate.startDate, endDate: this.selectedDate.endDate});
		},
		goback() {
			const d = new Date();
			this.initDate(d);
		}
	},
}
</script>

<style lang="scss" scoped>
.edream-calendar {
	background-color: #ffffff;
	padding-top: 10rpx;
	padding-bottom: 10rpx;
	position: relative;
	width: 100%;
	height: auto;
	// margin: 20rpx 0;
	.calendar-header {
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 60rpx;
		padding: 0 30rpx;
		color: #333333;
		font-size: 28rpx;
		font-weight: bold;
		.line {
			padding: 0 20rpx;
			color: #ccc;
		}
		.calendar-switch {
			position: relative;
			display: flex;
			align-items: center;
			width: 70rpx;
			height: 55rpx;
			background: #ECEDEE;
			box-shadow: 0rpx 0rpx 5rpx 0rpx rgba(51,51,51,0.26);
			border-radius: 5rpx;
			.switch-active {
				position: absolute;
				top: 6rpx;
				left: 1rpx;
				width: 70rpx;
				height: 45rpx;
				box-shadow: 0px 2px 5px 0px rgba(51,51,51,0.16);
				border-radius: 5px;
				background: #fff;
				transition: left ease-out 0.3s;
				&.mode-level-2 {
					left: 70rpx;
				}
				&.mode-level-3 {
					left: 140rpx;
				}
			}
			.switch-item {
				position: absolute;
				top: 0;
				width: 70rpx;
				height: 55rpx;
				line-height: 55rpx;
				font-size: 24rpx;
				text-align: center;
			}
			.day {
				left: 0;
			}
			.week {
				left: 70rpx
			}
			.month {
				left: 140rpx;
			}
		}
		.back-today {
			position: absolute;
			right: 0;
			width: 100rpx;
			height: 30rpx;
			line-height: 30rpx;
			font-size: 20rpx;
			top: 15rpx;
			border-radius: 15rpx 0 0 15rpx;
			color: #ffffff;
			background-color: #FF6633;
		}
	}
	.calendar-weeks {
		display: flex;
		flex-flow:row nowrap;
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;
		justify-content: center;
		align-items: center;
		font-size: 30rpx;
		.calendar-week {
			width: calc(100% / 7);
			height: 100%;
			text-align: center;
		}
	}
	swiper {
		width: 100%;
		height: 60rpx;
	}
	.calendar-content {
		min-height: 60rpx;
	}
	.calendar-swiper {       
		min-height: 70rpx;
		transition: height ease-out 0.3s;
	}
	.calendar-swiper-item {
		margin: 0;
		padding: 0;
		height: 100%;
	}
	.calendar-dates-wrapper {
		display: flex;
		flex-flow: row wrap;
		width: 100%;
		height: 100%;
		overflow: hidden;
		font-size: 28rpx;
		.calendar-day {
			width: calc(100% / 7);
			height: 70rpx;
			text-align: center;
			display: flex;
			flex-flow: column nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
	.day-perMonth {
		color: #ccc;
	}
	.date {
		width: 50rpx;
		height: 50rpx;
		line-height: 50rpx;
		margin: 0 auto;
		border-radius: 50rpx;
	}
	.calendar-month-bg {
		position: absolute;
		top: 50%;
		left: 50%;
		opacity: 0.2;
		transform: translate(-50%, -50%);
		color: #4578F9;
		font-size: 100rpx;
		font-weight: bolder;
		pointer-events: none;
	}
	.mode-change {
		position: relative;
		display: flex;
		justify-content: center;
		margin-top: 10rpx;
	}
	.arrow-line {
		width: 100%;
		height: 1rpx;
		margin: 4rpx 30rpx 0 30rpx;
		background: #ccc;
	}
	.arrow-icon {
		position: absolute;
		top: 10rpx;
		left: 50%;
		width: 30rpx;
		height: 20rpx;
		transform: translate(-50%, -50%);
		background: #fff;
		transition: transform ease-out 0.3s;
		&.active {
			transform: translate(-50%, -50%) rotate(180deg);
			transform-origin: center;
		}
	}
	.is-today {
		background: #ffffff;
		border-radius: 50%;
		color: #4578F9;
	}
	.is-checked {
		background: #4578F9;
		color: #ffffff;
	}
	.calendar-week-swiper {
		height: 260rpx;
	}
	.range-date-wrapper {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		flex-flow: row wrap;
		padding: 20rpx 30rpx;
		&.range-week-wrapper:after {
			content: '';
			padding: 10rpx;
			width: 28%;
		}
		.range-date-item {
			width: 28%;
			margin-bottom: 26rpx;
			padding: 10rpx;
			border-radius: 5rpx;
			text-align: center;
			&.is-today {
				.range-date-index {
					color: #4578F9;
				}
				.range-date-days {
					color: #4578F9;
				}
			}
			&.week-item {
				background: #f8f8f8;
				color: #333;
			}
			&.month-item {
				display: flex;
				justify-content: center;
				align-items:center;
				width: 70rpx;
				height: 70rpx;
				border-radius: 50%;
				padding: 0;
				background: #fff;
				color: #333;
				font-size: 24rpx;
			}
			&.is-checked {
				background: #4578f9;
				color: #fff;
				.range-date-index {
					color: #fff;
				}
				.range-date-days {
					color: #fff;
				}
				&.month-item {
					background: #4578F9;
				}
			}
		}
		.range-date-index {
			color: #333;
			font-weight: bold;
			font-size: 24rpx;
			text-align: center;
		}
		.range-date-days {
			color: #999;
			padding-top: 10rpx;
			font-size: 22rpx;
		}
	}
}
</style>
