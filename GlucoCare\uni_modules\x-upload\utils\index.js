// 获取父组件的参数，因为支付宝小程序不支持provide/inject的写法
// this.$parent在非H5中，可以准确获取到父组件，但是在H5中，需要多次this.$parent.$parent.xxx
// 这里默认值等于undefined有它的含义，因为最顶层元素(组件)的$parent就是undefined，意味着不传name
// 值(默认为undefined)，就是查找最顶层的$parent
// import validation from "@/uview-ui/libs/function/test";

export function $parent (name = undefined) {
    let parent = this.$parent;
    // 通过while历遍，这里主要是为了H5需要多层解析的问题
    while (parent) {
        // 父组件
        if (parent.$options && parent.$options.name !== name) {
            // 如果组件的name不相等，继续上一级寻找
            parent = parent.$parent;
        } else {
            return parent;
        }
    }
    return false;
}


/**
 * 是否json字符串
 */
export const jsonString = (value) => {
    if (typeof value == 'string') {
        try {
            var obj = JSON.parse(value);
            if (typeof obj == 'object' && obj) {
                return true;
            } else {
                return false;
            }
        } catch (e) {
            return false;
        }
    }
    return false;
}

/**
 * 验证十进制数字
 */
function number(value) {
    return /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(value)
}

// 添加单位，如果有rpx，%，px等单位结尾或者值为auto，直接返回，否则加上rpx单位结尾
export function addUnit(value = 'auto', unit = 'rpx') {
    value = String(value);
    // 用uView内置验证规则中的number判断是否为数值
    return number(value) ? `${value}${unit}` : value;
}
