<template>
	<view class="login-container">
		<view class="header">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="title">控糖智汇</text>
			<text class="subtitle">糖尿病知识相关科普</text>
		</view>
		
		<view class="login-content">
			<view class="welcome-text">
				<text class="welcome-title">欢迎使用</text>
				<text class="welcome-desc">请使用微信账号登录</text>
			</view>
			
			<view class="button-container">
				<button class="login-btn" @click="handleLogin" :disabled="loading">
					<text class="btn-text">{{loading ? '登录中...' : '微信登录'}}</text>
				</button>
				
				<view class="register-link">
					<text class="register-text">还没有账号？</text>
					<text class="register-action" @click="goToRegister">立即注册</text>
				</view>
			</view>
		</view>
		
		<view class="footer">
			<text class="footer-text">登录即表示同意用户协议和隐私政策</text>
		</view>
	</view>
</template>

<script>
	import { loginUser, checkUser } from '@/api/user/user.js'
	import { setToken, setUserInfo } from '@/utils/token.js'
	
	export default {
		data() {
			return {
				loading: false
			}
		},
		methods: {
			// 获取微信授权码
			getWechatCode() {
				return new Promise((resolve, reject) => {
					uni.login({
						provider: 'weixin',
						success: (res) => {
							if (res.code) {
								resolve(res.code)
							} else {
								reject(new Error('获取授权码失败'))
							}
						},
						fail: (err) => {
							reject(err)
						}
					})
				})
			},
			
			// 处理登录
			async handleLogin() {
				this.loading = true
				
				try {
					// 获取微信授权码
					const code = await this.getWechatCode()
					
					// 调用登录接口
					const response = await loginUser({ code })
					
					if (response.code === 200) {
						// 登录成功，保存用户信息
						const userInfo = response.user || response.data
						const token = response.token || 'wechat_token_' + userInfo.userId
						
						// 保存token和用户信息到本地存储
						setToken(token)
						setUserInfo(userInfo)
						if (userInfo.openid) {
							uni.setStorageSync('openid', userInfo.openid)
						}
						
						uni.showToast({
							title: '登录成功',
							icon: 'success'
						})
						
						// 延迟跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							})
						}, 1500)
						
					} else {
						// 登录失败，可能是用户未注册
						if (response.msg.includes('用户不存在')) {
							uni.showModal({
								title: '提示',
								content: '您还未注册，是否前往注册？',
								success: (res) => {
									if (res.confirm) {
										this.goToRegister()
									}
								}
							})
						} else {
							uni.showToast({
								title: response.msg || '登录失败',
								icon: 'none'
							})
						}
					}
					
				} catch (error) {
					console.error('登录失败:', error)
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			},
			
			// 跳转到注册页面
			goToRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			
			// 检查用户登录状态
			async checkLoginStatus() {
				try {
					const code = await this.getWechatCode()
					const response = await checkUser({ code })
					
					if (response.code === 200) {
						// 用户已注册，直接登录
						const userInfo = response.data
						const token = 'wechat_token_' + userInfo.userId
						setToken(token)
						setUserInfo(userInfo)
						
						uni.switchTab({
							url: '/pages/index/index'
						})
					}
				} catch (error) {
					// 用户未注册或检查失败，显示登录界面
					console.log('用户未注册或检查失败')
				}
			}
		},
		
		onLoad() {
			// 页面加载时检查登录状态
			// this.checkLoginStatus()
		}
	}
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
		padding: 80rpx 40rpx 40rpx;
	}
	
	.header {
		text-align: center;
		margin-bottom: 120rpx;
		
		.logo {
			width: 160rpx;
			height: 160rpx;
			margin-bottom: 40rpx;
		}
		
		.title {
			font-size: 48rpx;
			font-weight: bold;
			color: #fff;
			display: block;
			margin-bottom: 20rpx;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}
	
	.login-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		
		.welcome-text {
			text-align: center;
			margin-bottom: 80rpx;
			
			.welcome-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #fff;
				display: block;
				margin-bottom: 20rpx;
			}
			
			.welcome-desc {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
			}
		}
		
		.button-container {
			.login-btn {
				width: 100%;
				height: 88rpx;
				background-color: #fff;
				border-radius: 44rpx;
				border: none;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 40rpx;
				
				&:disabled {
					background-color: rgba(255, 255, 255, 0.7);
				}
				
				.btn-text {
					font-size: 32rpx;
					color: #667eea;
					font-weight: bold;
				}
			}
			
			.register-link {
				text-align: center;
				
				.register-text {
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.8);
					margin-right: 10rpx;
				}
				
				.register-action {
					font-size: 28rpx;
					color: #fff;
					text-decoration: underline;
				}
			}
		}
	}
	
	.footer {
		text-align: center;
		margin-top: 60rpx;
		
		.footer-text {
			font-size: 24rpx;
			color: rgba(255, 255, 255, 0.6);
		}
	}
</style>
