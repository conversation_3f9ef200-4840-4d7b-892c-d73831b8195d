<template>
  <view class="container">
    <!-- 发布动态 -->
    <view class="post-section">
      <view class="input-area">
        <textarea v-model="content" placeholder="这一刻的想法..." maxlength="500" />
      </view>
      <view class="image-area">
        <view class="image-list">
          <view v-for="(img, idx) in images" :key="idx" class="img-item">
            <image :src="img" mode="aspectFill" class="img-preview" />
            <view class="img-remove" @click="removeImage(idx)">×</view>
          </view>
          <view v-if="images.length < 9" class="img-add" @click="chooseImage">
            <text>+</text>
          </view>
        </view>
      </view>
      <button class="submit-btn" @click="submitPost" :disabled="submitting">{{ submitting ? '发布中...' : '发布' }}</button>
    </view>

    <!-- 动态列表 -->
    <view class="divider">最新动态</view>
    <view class="post-list">
      <view v-if="postList.length === 0" style="text-align:center;color:#bbb;padding:40rpx 0;">暂无动态</view>
      <view v-else>
        <view v-for="item in postList" :key="item.id" class="post-item" @click="showDetail(item)">
          <view class="post-header">
            <!-- <text class="user">用户ID: {{ item.userId }}</text> -->
            <text class="time">{{ item.createTime }}</text>
          </view>
          <view class="post-content">{{ item.content }}</view>
          <view class="post-images">
            <image v-for="(img, idx) in parseImages(item.imageUrls)" :key="idx" :src="img" mode="aspectFill" class="img-preview" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import postApi from '@/api/post/index.js'
import uploadApi from '@/api/sys/upload.js'
// 移除uni-popup相关import

export default {
  // 移除components: { uniPopup },
  data() {
    return {
      content: '',
      images: [],
      submitting: false,
      postList: []
    }
  },
  onLoad() {
    this.loadPosts();
  },
  methods: {
    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 9 - this.images.length,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: res => {
          this.uploadImages(res.tempFilePaths);
        }
      });
    },
    // 上传图片
    async uploadImages(paths) {
      for (let path of paths) {
        try {
          const res = await uploadApi.uploadImage(path);
          if (res.code === 200 && res.url) {
            this.images.push(res.url);
          } else {
            console.error('图片上传失败', res);
            uni.showToast({ title: '图片上传失败', icon: 'none' });
          }
        } catch (e) {
          console.error('图片上传异常', e);
          uni.showToast({ title: '图片上传失败', icon: 'none' });
        }
      }
    },
    // 移除图片
    removeImage(idx) {
      this.images.splice(idx, 1);
    },
    // 发布动态
    async submitPost() {
      const openid = uni.getStorageSync('openid');
      if (!openid) {
        uni.showToast({ title: '请先登录', icon: 'none' });
        setTimeout(() => {
          uni.reLaunch({ url: '/pages/login/login' });
        }, 800);
        return;
      }
      if (!this.content.trim() && this.images.length === 0) {
        uni.showToast({ title: '请填写内容或上传图片', icon: 'none' });
        return;
      }
      this.submitting = true;
      try {
        const res = await postApi.addPost({
          userId: openid,
          content: this.content,
          imageUrls: this.images.join(',')
        });
        if (res.code === 200) {
          uni.showToast({ title: '发布成功', icon: 'success' });
          this.content = '';
          this.images = [];
          this.loadPosts();
        } else {
          uni.showToast({ title: '发布失败', icon: 'none' });
        }
      } catch (e) {
        uni.showToast({ title: '网络错误', icon: 'none' });
      } finally {
        this.submitting = false;
      }
    },
    // 加载动态列表
    async loadPosts() {
      try {
        const openid = uni.getStorageSync('openid');
        const res = await postApi.getPostList({ userId: openid });
        if (res.code === 200) {
          this.postList = res.data || [];
          console.log('动态列表', this.postList);
        } else {
          uni.showToast({ title: '获取动态失败', icon: 'none' });
        }
      } catch (e) {
        console.error('获取动态异常', e);
        uni.showToast({ title: '获取动态失败', icon: 'none' });
      }
    },
    // 解析图片
    parseImages(urls) {
      if (!urls) return [];
      return urls.split(',').filter(Boolean);
    },
    // 展示动态详情
    showDetail(item) {
      const imgs = this.parseImages(item.imageUrls);
      // encodeURIComponent防止特殊字符出错
      uni.navigateTo({
        url: `/pages/post/detail?content=${encodeURIComponent(item.content || '')}&imgs=${encodeURIComponent(imgs.join(','))}&time=${encodeURIComponent(item.createTime || '')}`
      });
    }
  }
}
</script>

<style scoped>
.container { padding: 20rpx; background: #f5f7fa; min-height: 100vh; }
.post-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.input-area {
  width: 100%;
  display: flex;
  justify-content: center;
}
.input-area textarea {
  width: 90%;
  min-height: 100rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  padding: 16rpx 32rpx 16rpx 16rpx;
  font-size: 28rpx;
  background: #f9f9fb;
  box-shadow: 0 2rpx 8rpx 0 rgba(0,0,0,0.03);
}
.image-area { margin: 16rpx 0; }
.image-list { display: flex; flex-wrap: wrap; gap: 16rpx; }
.img-item { position: relative; }
.img-preview { width: 140rpx; height: 140rpx; border-radius: 8rpx; }
.img-remove { position: absolute; top: -10rpx; right: -10rpx; background: #f56c6c; color: #fff; border-radius: 50%; width: 36rpx; height: 36rpx; display: flex; align-items: center; justify-content: center; font-size: 28rpx; }
.img-add { width: 140rpx; height: 140rpx; background: #f8f8f8; border: 1rpx dashed #bbb; border-radius: 8rpx; display: flex; align-items: center; justify-content: center; font-size: 60rpx; color: #bbb; }
.submit-btn { width: 100%; background: #4CAF50; color: #fff; border: none; border-radius: 8rpx; padding: 20rpx 0; font-size: 32rpx; margin-top: 20rpx; }
.divider { margin: 32rpx 0 16rpx; color: #888; font-size: 28rpx; }
.post-list { }
.post-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 24rpx 0 rgba(0,0,0,0.08);
  transition: box-shadow 0.2s;
}
.post-item:active {
  box-shadow: 0 8rpx 32rpx 0 rgba(0,0,0,0.12);
}
.post-header { display: flex; justify-content: space-between; color: #999; font-size: 24rpx; margin-bottom: 8rpx; }
.post-content { font-size: 30rpx; margin-bottom: 8rpx; }
.post-images { display: flex; flex-wrap: wrap; gap: 12rpx; }
</style> 