<!-- https://ext.dcloud.net.cn/plugin?id=1732 -->
<template>
	<view class="zzx-calendar">
		<view class="calendar-heander">
			<view class="calendar-left">
				<text class="pre-year iconfont icon-a-doubleleft2x" @click="previousYear"
					v-show="showYearControl"></text>
				<text class="pre-month iconfont icon-a-left2x" @click="previousMonth" v-show="showMonthControl"></text>
			</view>
			<view class="z-calendar-center">
				{{ timeStr }}
			</view>
			<view class="calendar-right">
				<text class="next-month iconfont icon-a-right2x" @click="nextMonth" v-show="showMonthControl"></text>
				<text class="next-year iconfont icon-a-doubleright2x" @click="nextYear" v-show="showYearControl"></text>
			</view>
			<view class="back-today" @click="goback" v-if="showBack">
				本月
			</view>
		</view>
		<view class="calendar-weeks">
			<view class="calendar-week" v-for="(week, index) in weeks" :key="index">
				{{week}}
			</view>
		</view>
		<view class="calendar-content">
			<swiper class="calendar-swiper" :style="{
			   width: '100%',
			   height: sheight
		   }" :indicator-dots="false" :autoplay="false" :duration="duration" :current="current" @change="changeSwp"
				:circular="true">
				<swiper-item class="calendar-item" v-for="sitem in swiper" :key="sitem">
					<view class="calendar-days">
						<template v-if="sitem === current">
							<view class="calendar-day" v-for="(item,index) in days" :key="index" :class="{'full-date':!item.show  || item.disabled}" @click="clickItem(item)">
								<view class="date" :class="[ item.isToday ? todayClass : '', item.fullDate === selectedDate ? checkedClass : '' ]">
									{{item.time.getDate()}}
								</view>
								<!-- 打点 -->
								<view class="dot-show" v-if="item.info" :style="[dotStyle]"></view>
							</view>
						</template>
						<template v-else>
							<template v-if="current - sitem === 1 || current-sitem ===-2">
								<view class="calendar-day" v-for="(item,index) in predays" :key="index" :class="{
										'full-date':!item.show || item.disabled
									}">
									<view class="date" :class="[
											item.isToday ? todayClass : ''
											]">
										{{item.time.getDate()}}
									</view>
								</view>
							</template>
							<template v-else>
								<view class="calendar-day" v-for="(item,index) in nextdays" :key="index" :class="{
										'full-date':!item.show || item.disabled
									}">
									<view class="date" :class="[
											item.isToday ? todayClass : ''
											]">
										{{item.time.getDate()}}
									</view>
								</view>
							</template>

						</template>
					</view>
				</swiper-item>
			</swiper>
			<view class="mode-change" @click="changeMode">
				<view :class="weekMode ? 'mode-arrow-bottom' : 'mode-arrow-top'">
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		gegerateDates,
		dateEqual,
		formatDate,
		dateComparison
	} from './generateDates.js';
	export default {
		props: {
			// showFullControl: {
			// 	type: Boolean,
			// 	default: true, // 是否显示有所的日期，只对月视图有效，显示上个月和下个月的日期
			// },
			showYearControl: {
				type: Boolean,
				default: false, // 是否显示上一年下一年控制按钮
			},
			showMonthControl: {
				type: Boolean,
				default: true, // 是否显示上一月下一月控制按钮
			},
			duration: {
				type: Number,
				default: 500
			},
			dotList: {
				type: Array, /// 打点日期列表
				default () {
					return []
				}
			},
			showBack: {
				type: Boolean, // 是否返回今日
				default: true
			},
			todayClass: {
				type: String, // 今日的自定义样式class
				default: 'is-today'
			},
			checkedClass: {
				type: String, // 选中日期的样式class
				default: 'is-checked'
			},
			dotStyle: {
				type: Object, // 打点日期的自定义样式
				default () {
					return {
						background: '#c6c6c6' //'#c6c6c6'
					}
				}
			}
		},
		watch: {
			dotList: function(newvalue) {
				const days = this.days.slice(0);
				newvalue.forEach(item => {
					const index = days.findIndex(ditem => ditem.fullDate === item.date);
					if (index >= 0) {
						days[index].info = true
					}else{
						days[index].info=false
					}
				});
				this.days = days;
				//新增的
				//this.days.forEach(item=>{ item.info=undefined });
			}
		},
		computed: {
			sheight() {
				// 根据年月判断有多少行
				// 判断该月有多少天
				let h = '70rpx';
				if (!this.weekMode) {
					const d = new Date(this.currentYear, this.currentMonth, 0);
					const days = d.getDate(); // 判断本月有多少天
					let day = new Date(d.setDate(1)).getDay();
					if (day === 0) {
						day = 7;
					}
					const pre = 8 - day;
					const rows = Math.ceil((days - pre) / 7) + 1;
					h = 60 * rows + 'rpx'
				}
				return h
			},
			timeStr() {
				let str = '';
				const d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate);
				const y = d.getFullYear();
				const m = (d.getMonth() + 1) <= 9 ? `0${d.getMonth()+1}` : d.getMonth() + 1;
				str = `${y}年${m}月`;
				return str;
			},
			predays() {
				//computed
				let pres = [];
				if (this.weekMode) {
					const d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate)
					if (this.changMonth) {
						d.setDate(d.getDate() - 7);
					}
					pres = gegerateDates(d, 'week')
				} else {
					const d = new Date(this.currentYear, this.changMonth ? this.currentMonth - 2 : this.currentMonth, 1)
					pres = gegerateDates(d, 'month')
				}
				return pres;
			},
			nextdays() {
				//computed
				let nexts = [];
				if (this.weekMode) {
					const d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate)
					if (this.changMonth) {
						d.setDate(d.getDate() + 7);
					}
					nexts = gegerateDates(d, 'week')
				} else {
					const d = new Date(this.currentYear, this.currentMonth, 1)
					nexts = gegerateDates(d, 'month')
				}
				return nexts;
			}
		},
		data() {
			return {
				weeks: ['一', '二', '三', '四', '五', '六', '日'],
				current: 1,
				currentYear: '',
				currentMonth: '', //中文的加了1的
				currentDate: '',
				days: [],
				weekMode: true,
				changMonth: false, //是否同时更新月份
				swiper: [0, 1, 2],
				// dotList: [], // 打点的日期列表
				selectedDate: formatDate(new Date(), 'yyyy-MM-dd')
			};
		},
		methods: {
			previousYear() {
				this.currentYear = this.currentYear - 1
				this.changMonth = false
				this.daysPre();
			},
			previousMonth() {
				this.changMonth = true
				this.daysPre();
			},
			nextMonth() {
				this.changMonth = true
				this.daysNext();
			},
			nextYear() {
				this.changMonth = false
				this.currentYear = this.currentYear + 1
				this.daysNext();
			},
			changeSwp(e) {
				// console.log(e);
				const pre = this.current;
				const current = e.target.current;
				/* 根据前一个减去目前的值我们可以判断是下一个月/周还是上一个月/周 
				 *current - pre === 1, -2时是下一个月/周
				 *current -pre === -1, 2时是上一个月或者上一周
				 */
				this.current = current;
				if (current - pre === 1 || current - pre === -2) {
					this.changMonth = true
					this.daysNext();
				} else {
					this.changMonth = true
					this.daysPre();
				}
			},
			// 初始化日历的方法
			initDate(cur) {
				let date = ''
				if (cur) {
					date = new Date(cur)
				} else {
					date = new Date()
				}
				this.currentDate = date.getDate() // 今日日期 几号
				this.currentYear = date.getFullYear() // 当前年份
				this.currentMonth = date.getMonth() + 1 // 当前月份
				this.currentWeek = date.getDay() === 0 ? 7 : date.getDay() // 1...6,0   // 星期几
				// const nowY = new Date().getFullYear()       // 当前年份
				// const nowM = new Date().getMonth() + 1
				// const nowD = new Date().getDate()          // 今日日期 几号
				// const nowW = new Date().getDay()

				// this.selectedDate = formatDate(new Date(), 'yyyy-MM-dd')
				this.days = [];
				let days = [];
				if (this.weekMode) {
					days = gegerateDates(date, 'week');
					// this.selectedDate = days[0].fullDate;
				} else {
					days = gegerateDates(date, 'month');
					// const sel = new Date(this.selectedDate.replace('-', '/').replace('-', '/'));
					// const isMonth = sel.getFullYear() === this.currentYear && (sel.getMonth() + 1) === this.currentMonth;
					// if(!isMonth) {
					// 	this.selectedDate = formatDate(new Date(this.currentYear, this.currentMonth-1,1), 'yyyy-MM-dd')
					// }
				}
				const nowDate = formatDate(new Date(), 'yyyy-MM-dd')
				days.forEach(day => {
					//遍历result判断日期是否在当前日期之前如果在当前日期之前则直接禁用
					// day.disabled = dateComparison(nowDate,day.fullDate)
					
					//是否显示所有的日期（对于月视图而言）
					//   if(this.showFullControl){
					//   	day.show = true
					//   }
					
					//打点
					this.dotList.find(item => {
						const dot = dateEqual(item.date, day.fullDate);
						if (dot) {
							day.info = dot;
						}
					})
				})
				this.days = days;
				//  派发事件,时间发生改变
				let obj = {
					start: '',
					end: ''
				};
				if (this.weekMode) {
					obj.start = this.days[0].time;
					obj.end = this.days[6].time
				} else {
					const start = new Date(this.currentYear, this.currentMonth - 1, 1);
					const end = new Date(this.currentYear, this.currentMonth, 0);
					obj.start = start;
					obj.end = end;
				}
				this.$emit('days-change', obj)
			},
			//  上一个
			daysPre() {

				if (this.weekMode) {
					const d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate);
					if (this.changMonth) {
						d.setDate(d.getDate() - 7);
					}
					this.initDate(d);
				} else {
					const d = new Date(this.currentYear, this.changMonth ? this.currentMonth - 2 : this.currentMonth - 1,
						1);
					this.initDate(d);
				}
			},
			//  下一个 changMonth:是否改变月份 true:改变 false:不改变
			daysNext() {
				if (this.weekMode) {
					const d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate);
					if (this.changMonth) {
						d.setDate(d.getDate() + 7);
					}
					this.initDate(d);
				} else {
					const d = new Date(this.currentYear, this.changMonth ? this.currentMonth : this.currentMonth - 1, 1);
					this.initDate(d);
				}
			},
			changeMode() {
				console.log("1111111111")
				const premode = this.weekMode;
				let isweek = false;
				if (premode) {
					isweek = !!this.days.find(item => item.fullDate === this.selectedDate)
				}
				this.weekMode = !this.weekMode;
				let d = new Date(this.currentYear, this.currentMonth - 1, this.currentDate)
				const sel = new Date(this.selectedDate.replace('-', '/').replace('-', '/'));
				const isMonth = sel.getFullYear() === this.currentYear && (sel.getMonth() + 1) === this.currentMonth;
				if ((this.selectedDate && isMonth) || isweek) {
					d = new Date(this.selectedDate.replace('-', '/').replace('-', '/'))
				}
				this.initDate(d)
			},
			// 点击日期
			clickItem(e) {
				//非本月的不能选中,或者今天之前的不能选择
				if(!e.show || e.disabled){
					return;
				}
				this.selectedDate = e.fullDate;
				this.$emit('selected-change', e);
			},
			goback() {
				const d = new Date();
				this.initDate(d);
			}
		},
		created() {
			this.initDate();
		},
		mounted() {}
	}
</script>

<style lang="scss" scoped>
	@import "./fonts/iconfont.css";

	.zzx-calendar {
		width: 100%;
		height: auto;
		background-color: #ffffff;
		box-shadow: 0px 4px 10px  #e3e5e6;
		.calendar-heander {
			text-align: center;
			height: 60upx;
			line-height: 60upx;
			position: relative;
			font-size: 25upx;

			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-content: center;
			font-size: 34rpx;
			font-weight: 500;
			color: #000000;
			height: 60rpx;
			line-height: 60rpx;

			.z-calendar-center {
				flex: 1;
		 	text-align: center;
			}

			.back-today {
				position: absolute;
				right: 0;
				width: 100upx;
				height: 30upx;
				line-height: 30upx;
				font-size: 30rpx;
				top: 15upx;
				border-radius: 15upx 0 0 15upx;
				color: #191919;
				// background-color: #FF6633;
			}

			.calendar-left {
				text-align: left;
				padding-left: 20%;

				.iconfont {
					font-family: "iconfont" !important;
					font-size: 16px;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
				}

				.pre-month {
					margin-left: 40rpx;
				}
			}

			.calendar-right {
				text-align: right;
				padding-right: 20%;

				.iconfont {
					font-family: "iconfont" !important;
					font-size: 16px;
					font-style: normal;
					-webkit-font-smoothing: antialiased;
					-moz-osx-font-smoothing: grayscale;
				}

				.next-month {
					margin-right: 40rpx;
				}
			}
		}

		.calendar-weeks {
			width: 100%;
			display: flex;
			flex-flow: row nowrap;
			height: 60upx;
			line-height: 60upx;
			justify-content: center;
			align-items: center;
			color: rgba(143, 149, 158, 1);
			font-size: 28rpx;
			font-weight: 400;
			letter-spacing: 0px;
			.calendar-week {
				width: calc(100% / 7);
				height: 100%;
				text-align: center;
			}
		}

		swiper {
			width: 100%;
			height: 60upx;
		}

		.calendar-content {
			min-height: 60upx;
			border-bottom: 1upx solid #f7f7f7;
			border-bottom-left-radius: 37upx;
			border-bottom-right-radius: 37upx;
		}

		.calendar-swiper {
			min-height: 70upx;
			transition: height ease-out 0.3s;
		}

		.calendar-item {
			margin: 0;
			padding: 0;
			height: 100%;
		}

		.calendar-days {
			display: flex;
			flex-flow: row wrap;
			width: 100%;
			height: 100%;
			overflow: hidden;
			font-size: 28upx;

			.calendar-day {
				width: calc(100% / 7);
				// height: 70upx;
				text-align: center;
				display: flex;
				flex-flow: column nowrap;
				justify-content: flex-start;
				align-items: center;
			}
		}

		.day-hidden {
			visibility: hidden;
		}
		
		.full-date{
			color: #c6c6c6;
		}

		.mode-change {
			display: flex;
			justify-content: center;
			margin-top: 10upx;
			margin-bottom: 22upx;

			.mode-arrow-top {
				width: 0;
				height: 0;
				border-left: 12upx solid transparent;
				border-right: 12upx solid transparent;
				border-bottom: 10upx solid #9da2aa;
			}

			.mode-arrow-bottom {
				width: 0;
				height: 0;
				border-left: 12upx solid transparent;
				border-right: 12upx solid transparent;
				border-top: 10upx solid #9da2aa;
			}
		}

		.is-today {
			background: #ffffff;
			border: 1upx solid #4876FF;
			border-radius: 50%;
			color: #4876FF;
		}

		.is-checked {
			background: #4876FF;
			color: #ffffff;
		}

		.date {
			width: 50upx;
			height: 50upx;
			line-height: 50upx;
			margin: 0 auto;
			border-radius: 50upx;
			font-size: 25upx;
		}

		.dot-show {
			margin-top: 4upx;
			width: 10upx;
			height: 10upx;
			background: #c6c6c6;//#c6c6c6
			border-radius: 10upx;
		}
	}
</style>
