## 1.0.10（2024-02-04）
滚动选择时间优化
## 1.0.9（2023-10-14）
1、最大最小时间支持限制时分秒；
2、可选列生成优化。
## 1.0.8（2023-08-10）
修复选择器打开后直接点击确定，日期返回undefined的bug
## 1.0.7（2023-08-06）
新增 weekType 属性，当 type ="week" 时定义周类型
## 1.0.6（2023-07-20）
修复首次打开时未正确展示默认时间的bug
## 1.0.5（2023-07-02）
新增周选择：type="week"
## 1.0.4（2023-05-24）
1、修复 type = year-month-range 时，日期展示undefined的bug；
2、新增适配底部安全区属性：safeArea。
## 1.0.3（2023-05-19）
1、修复最大最小时间相同时最小时间不生效的bug；
2、修复当前时间为最大最小时间时，切换年份导致当前选中日期乱跳的bug。
## 1.0.2（2023-05-18）
新增年范围选择：type = year-range
## 1.0.1（2023-05-10）
新增年、月范围选择：type = year-month-range
## 1.0.0（2023-03-29）
lingfeng-timepicker 时间选择器
