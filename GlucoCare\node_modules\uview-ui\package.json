{"_from": "uview-ui@2.0.36", "_id": "uview-ui@2.0.36", "_inBundle": false, "_integrity": "sha512-ASSZT6M8w3GTO1eFPbsgEFV0U5UujK+8pTNr+MSUbRNcRMC1u63DDTLJVeArV91kWM0bfAexK3SK9pnTqF9TtA==", "_location": "/uview-ui", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "uview-ui@2.0.36", "name": "uview-ui", "escapedName": "uview-ui", "rawSpec": "2.0.36", "saveSpec": null, "fetchSpec": "2.0.36"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmmirror.com/uview-ui/-/uview-ui-2.0.36.tgz", "_shasum": "c821e73aa79bf62b55bf40b769ac02a07a3a755e", "_spec": "uview-ui@2.0.36", "_where": "D:\\code\\demo\\RuoYi\\Organ-App\\organApp", "bugs": {"url": "https://github.com/umicro/uView2.0/issues"}, "bundleDependencies": false, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "1416956117"}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/uview-ui"}, "deprecated": false, "description": "uView UI已完美兼容nvue，全面的组件和便捷的工具会让您信手拈来，如鱼得水", "displayName": "uView2.0重磅发布，利剑出鞘，一统江湖", "engines": {"HBuilderX": "^3.1.0"}, "homepage": "https://github.com/umicro/uView2.0#readme", "id": "uview-ui", "keywords": ["uview", "uview", "ui", "ui", "uni-app", "uni-app", "ui"], "name": "uview-ui", "repository": {"type": "git", "url": "git+https://github.com/umicro/uView2.0.git"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "n"}, "App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}}}}, "version": "2.0.36"}