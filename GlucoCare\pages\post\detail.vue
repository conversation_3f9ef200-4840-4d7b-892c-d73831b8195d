<template>
  <view class="detail-container">
    <view class="detail-content">{{ content }}</view>
    <view v-if="images.length" class="detail-images">
      <image v-for="(img, idx) in images" :key="idx" :src="img" mode="aspectFit" class="detail-img" />
    </view>
    <view class="detail-time">{{ time }}</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      content: '',
      images: [],
      time: ''
    }
  },
  onLoad(query) {
    this.content = decodeURIComponent(query.content || '');
    this.time = decodeURIComponent(query.time || '');
    const imgs = decodeURIComponent(query.imgs || '');
    this.images = imgs ? imgs.split(',').filter(Boolean) : [];
  }
}
</script>

<style scoped>
.detail-container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 40rpx 24rpx 80rpx 24rpx;
  position: relative;
}
.detail-content {
  font-size: 32rpx;
  color: #222;
  margin-bottom: 32rpx;
  white-space: pre-line;
}
.detail-images {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.detail-img {
  width: 100%;
  max-height: 400rpx;
  border-radius: 12rpx;
  object-fit: contain;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
}
.detail-time {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  background: rgba(0,0,0,0.65);
  color: #fff;
  font-size: 26rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  z-index: 99;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.10);
}
</style> 