<template>
  <view class="container">
    <view v-for="item in list" :key="item.id" class="prevention-card" @click="goDetail(item)">
      <image v-if="item.coverImg" :src="item.coverImg" class="cover" mode="aspectFill"/>
      <view class="info">
        <view class="title">{{ item.title }}</view>
        <view class="meta">{{ item.createTime }}</view>
        <view class="desc">{{ item.contentText }}</view>
      </view>
    </view>
  </view>
</template>
<script>
import { getPreventionList } from './index.js'
export default {
  data() {
    return { list: [] }
  },
  onLoad() {
    getPreventionList({ status: '0' }).then(res => {
      this.list = (res.rows || []).map(item => {
        // 去除所有<img ...>标签和其它HTML标签
        let text = item.content ? item.content.replace(/<img[^>]*>/gi, '').replace(/<[^>]+>/g, '') : '';
        text = text.trim();
if (text.length <= 40) {
  item.contentText = text;
} else {
  item.contentText = text.slice(0, 40) + '...';
}
        return item;
      });
    })
  },
  methods: {
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/prevention/detail?id=${item.id}`
      })
    }
  }
}
</script>
<style scoped>
.container { padding: 24rpx; }
.prevention-card {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx #eee;
  padding: 20rpx;
  align-items: flex-start;
}
.cover {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  object-fit: cover;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.meta {
  font-size: 24rpx;
  color: #aaa;
  margin-bottom: 8rpx;
}
.desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
