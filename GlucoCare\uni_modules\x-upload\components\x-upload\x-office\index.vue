<template>
<view class="x-pdf" v-if="visible">
  <u-popup  v-model="visible"
            :closeable="true"
            duration="0"
            :height="height"
            mode="bottom">
    <view style="overflow: hidden">
      <web-view :webview-styles="webviewStyles" :src="src"></web-view>
    </view>
  </u-popup>
</view>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      default: 'https://oss.newpearl.com/newpearl/file/2022-04-18/80e7ec783be14b489fcfcafd9fe3b580.PDF'
    }
  },
  data() {
    return {
      webviewStyles: {
        // width: '100%',
        // height: '100%'
      },
      visible: false,
      height: '100%'
    };
  },
  computed: {
    src () {
      if (!this.url) return '';
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(this.url)}`
      // return 'https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(this.url);
    }
  },
  mounted() {
    document.body.append(this.$el);
  },
  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  }
};
</script>

<style scoped lang="scss">
.x-pdf{
  height: 100vh;
  overflow: scroll;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;

  ::v-deep .uni-scroll-view-content{
    background: #4a4a4a;
  }
}
</style>
