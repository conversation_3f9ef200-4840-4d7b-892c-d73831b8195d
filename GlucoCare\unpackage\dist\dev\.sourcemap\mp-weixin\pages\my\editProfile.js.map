{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?f788", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?b2dd", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?2ee5", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?0b4d", "uni-app:///pages/my/editProfile.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?86d0", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/my/editProfile.vue?3354"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "form", "gender", "age", "height", "weight", "phone", "diabetes<PERSON><PERSON>s", "genderOptions", "onLoad", "userId", "methods", "submitForm", "uni", "title", "icon", "url", "method", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,6pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACmChrB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;MACAC;MAAA;MACAR;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAI;IACAC;MAAA;MACA;MACA;QACAC;UAAAC;UAAAC;QAAA;QACA;MACA;MACA;MACA;QACAC;QACAC;QACAjB;MACA;QACA;UACA;UACA;UACAa;YAAAC;YAAAC;UAAA;UACAG;YACAL;UACA;QACA;UACAA;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAA49B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAh/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/editProfile.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/editProfile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./editProfile.vue?vue&type=template&id=4feb21d6&scoped=true&\"\nvar renderjs\nimport script from \"./editProfile.vue?vue&type=script&lang=js&\"\nexport * from \"./editProfile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./editProfile.vue?vue&type=style&index=0&id=4feb21d6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4feb21d6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/editProfile.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./editProfile.vue?vue&type=template&id=4feb21d6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./editProfile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./editProfile.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"edit-profile-container\">\r\n    <view class=\"form-section\">\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">性别</text>\r\n        <picker :range=\"genderOptions\" v-model=\"form.gender\">\r\n          <view class=\"picker-value\">{{ genderOptions[form.gender] }}</view>\r\n        </picker>\r\n      </view>\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">年龄</text>\r\n        <input class=\"input\" type=\"number\" v-model=\"form.age\" placeholder=\"请输入年龄\" />\r\n      </view>\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">身高(cm)</text>\r\n        <input class=\"input\" type=\"number\" v-model=\"form.height\" placeholder=\"请输入身高\" />\r\n      </view>\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">体重(kg)</text>\r\n        <input class=\"input\" type=\"number\" v-model=\"form.weight\" placeholder=\"请输入体重\" />\r\n      </view>\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">联系方式</text>\r\n        <input class=\"input\" type=\"text\" v-model=\"form.phone\" placeholder=\"请输入联系方式\" />\r\n      </view>\r\n      <view class=\"form-item\">\r\n        <text class=\"label\">患病时长(年)</text>\r\n        <input class=\"input\" type=\"number\" v-model=\"form.diabetesYears\" placeholder=\"请输入患病时长\" />\r\n      </view>\r\n    </view>\r\n    <button class=\"save-btn\" @click=\"submitForm\">保存</button>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { getUserInfo, setUserInfo } from '@/utils/token.js'\r\nimport request from '@/utils/request.js'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        gender: 0,\r\n        age: '',\r\n        height: '',\r\n        weight: '',\r\n        phone: '',\r\n        diabetesYears: ''\r\n      },\r\n      genderOptions: ['男', '女']\r\n    }\r\n  },\r\n  onLoad() {\r\n    const userInfo = getUserInfo() || {}\r\n    this.form = {\r\n      userId: userInfo.userId, // 新增userId字段\r\n      gender: userInfo.gender || 0,\r\n      age: userInfo.age || '',\r\n      height: userInfo.height || '',\r\n      weight: userInfo.weight || '',\r\n      phone: userInfo.phone || '',\r\n      diabetesYears: userInfo.diabetesYears || ''\r\n    }\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      // 简单校验\r\n      if (!this.form.age || !this.form.height || !this.form.weight) {\r\n        uni.showToast({ title: '请填写完整信息', icon: 'none' })\r\n        return\r\n      }\r\n      // 调用后端API保存\r\n      request({\r\n        url: '/wechat/user/profile',\r\n        method: 'put',\r\n        data: this.form\r\n      }).then(res => {\r\n        if (res.code === 200) {\r\n          // 更新本地缓存\r\n          setUserInfo({ ...getUserInfo(), ...this.form })\r\n          uni.showToast({ title: '保存成功', icon: 'success' })\r\n          setTimeout(() => {\r\n            uni.navigateBack()\r\n          }, 800)\r\n        } else {\r\n          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.edit-profile-container { padding: 40rpx; }\r\n.form-section { background: #fff; border-radius: 20rpx; padding: 30rpx; margin-bottom: 40rpx; }\r\n.form-item { display: flex; align-items: center; margin-bottom: 30rpx; }\r\n.label { width: 160rpx; color: #666; font-size: 28rpx; }\r\n.input, .picker-value { flex: 1; border: 1px solid #eee; border-radius: 8rpx; padding: 16rpx; font-size: 28rpx; }\r\n.save-btn { width: 100%; height: 88rpx; background: #667eea; color: #fff; font-size: 32rpx; border-radius: 44rpx; border: none; }\r\n</style> ", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./editProfile.vue?vue&type=style&index=0&id=4feb21d6&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./editProfile.vue?vue&type=style&index=0&id=4feb21d6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973583158\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}