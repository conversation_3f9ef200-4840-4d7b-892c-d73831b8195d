# x-upload

#### 参数配置

属性名 | 类型 | 可选值 | 默认值 | 说明
---|:--:|---:|---:|---:
showUploadList | Boolean | true/false | true | 是否显示组件自带的图片预览功能
customBtn | Boolean | true/false | false | 是否通过slot自定义传入选择图标的按钮
disabled | Boolean | true/false | false | 是否启用
uploadText | String |  |  | 上传区域的提示文字
width | [String, Number] |  | 175 | 内部预览图片区域和选择图片按钮的区域宽度
height | [String, Number] |  | 175 | 内部预览图片区域和选择图片按钮的区域
maxCount | Number |  | 3 | 最大上传数量
maxSize | String |  | 10485760 | 文件大小限制，单位为byte
action | String |  |  | 后端地址
fileList | Array |  |  | 显示已上传的文件列表
limitType | Array |  | ['jpg', 'png', 'gif', 'jpeg', 'bmp', 'tiff', 'psd', 'eps', 'tga'] | 允许上传的图片后缀
deletable | Boolean | true/false | true | 是否展示删除按钮
beforeUpload | Function |  |  | 上传前的钩子，每个文件上传前都会执行
  
# 代码块

```
  <template>
  <view class="x-upload">
    <h3>上传</h3>
    <x-upload :fileList.sync="fileList"
              :maxCount="10"
              :width="160"
              :height="160"
              action="/api/core/uploadFileMinIO.do"
              maxSize="1048576000"
              :limitType="['jpg', 'png', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'zip', 'text', 'xls', 'xlsx']"
              :beforeUpload="beforeUpload"
              @success="handleSuccess"
              @error="handleError"
              @remove="handleRemove"></x-upload>
    <h3>只能预览</h3>
    <x-upload :fileList.sync="fileList"
  			:maxCount="fileList.length"
        :deletable="false"
  			:width="160"
  			:height="160"
  			action="/api/core/uploadFileMinIO.do"
  			maxSize="1048576000"
  			:limitType="['jpg', 'png', 'pdf', 'doc', 'docx', 'ppt', 'pptx', 'zip', 'text', 'xls', 'xlsx']"
  			:beforeUpload="beforeUpload"
  			@success="handleSuccess"
  			@error="handleError"
  			@remove="handleRemove"></x-upload>
    </view>
  </template>
  
  <script>
  import XUpload from '../uni_modules/x-upload/components/x-upload'
  import XPdf from '../uni_modules/x-pdf/components/x-pdf'
  export default {
    components:{XUpload},
    data() {
      return {
        value: 'week',
        visible: true,
        fileList: [
          {
            filePath: 'https://oss.newpearl.com/newpearl/img/2022-03-08/e75154abb096472f8882563f268b342e.png'
          },
          {
            filePath: 'https://oss.newpearl.com/newpearl/file/2022-04-18/80e7ec783be14b489fcfcafd9fe3b580.PDF'
          }
        ]
      };
    },
    methods: {
      beforeUpload() {
        uni.showLoading({
          title: '上传中...'
        });
      },
      handleSuccess(data, index) {
        uni.hideLoading();
        this.fileList.push(data)
      },
      handleError() {
        uni.hideLoading()
      },
      handleRemove(index) {
        this.fileList.splice(index, 1);
      }
    }
  };
  </script>
  
  <style>
  	/* 如果使用position: fixed需要在app.vue文件加入一下样式 */
  	/* iframe{
  	  width: 100% !important;
  	  position: fixed !important;
  	  z-index: 999999 !important;
  	  top: 6% !important;
  	  left: 0 !important;
  	  height: 94% !important;
  	}
  
  	.x-pdf uni-modal{
  	  z-index: 19999 !important;
  	}
  
  	.x-pdf .u-close{
  	  line-height: 1;
  	} */
  </style>
  
  
```
