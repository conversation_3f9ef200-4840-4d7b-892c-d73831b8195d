{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?e555", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?5eaa", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?f18a", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?64cf", "uni-app:///pages/prevention/list.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?682f", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/list.vue?ac60"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list", "onLoad", "status", "text", "item", "methods", "goDetail", "uni", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;;;AAGxF;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqpB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACazqB;;;;;;;;;;;;;eACA;EACAC;IACA;MAAAC;IAAA;EACA;EACAC;IAAA;IACA;MAAAC;IAAA;MACA;QACA;QACA;QACAC;QACA;UACAC;QACA;UACAA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAq9B,CAAgB,m7BAAG,EAAC,C;;;;;;;;;;;ACAz+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/prevention/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prevention/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=49930398&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=49930398&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"49930398\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prevention/list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=49930398&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view v-for=\"item in list\" :key=\"item.id\" class=\"prevention-card\" @click=\"goDetail(item)\">\r\n      <image v-if=\"item.coverImg\" :src=\"item.coverImg\" class=\"cover\" mode=\"aspectFill\"/>\r\n      <view class=\"info\">\r\n        <view class=\"title\">{{ item.title }}</view>\r\n        <view class=\"meta\">{{ item.createTime }}</view>\r\n        <view class=\"desc\">{{ item.contentText }}</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { getPreventionList } from './index.js'\r\nexport default {\r\n  data() {\r\n    return { list: [] }\r\n  },\r\n  onLoad() {\r\n    getPreventionList({ status: '0' }).then(res => {\r\n      this.list = (res.rows || []).map(item => {\r\n        // 去除所有<img ...>标签和其它HTML标签\r\n        let text = item.content ? item.content.replace(/<img[^>]*>/gi, '').replace(/<[^>]+>/g, '') : '';\r\n        text = text.trim();\r\nif (text.length <= 40) {\r\n  item.contentText = text;\r\n} else {\r\n  item.contentText = text.slice(0, 40) + '...';\r\n}\r\n        return item;\r\n      });\r\n    })\r\n  },\r\n  methods: {\r\n    goDetail(item) {\r\n      uni.navigateTo({\r\n        url: `/pages/prevention/detail?id=${item.id}`\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.container { padding: 24rpx; }\r\n.prevention-card {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin-bottom: 24rpx;\r\n  box-shadow: 0 2rpx 8rpx #eee;\r\n  padding: 20rpx;\r\n  align-items: flex-start;\r\n}\r\n.cover {\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  border-radius: 12rpx;\r\n  object-fit: cover;\r\n  margin-right: 20rpx;\r\n  flex-shrink: 0;\r\n}\r\n.info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n.title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 8rpx;\r\n  line-height: 1.2;\r\n}\r\n.meta {\r\n  font-size: 24rpx;\r\n  color: #aaa;\r\n  margin-bottom: 8rpx;\r\n}\r\n.desc {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  line-height: 1.4;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n</style>\r\n", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=49930398&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&id=49930398&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973583160\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}