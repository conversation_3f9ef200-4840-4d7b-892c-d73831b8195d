<template>
  <view class="home-container">
    <!-- 顶部欢迎区域 -->
    <view class="header-section">
      <view class="welcome-text">
        <text class="greeting">您好，{{userInfo.userName || '用户'}}</text>
        <text class="subtitle">欢迎使用GlucoCare血糖管理助手</text>
      </view>
      <image class="header-bg" src="/static/home-bg.png" mode="aspectFill"></image>
    </view>

    <!-- 功能模块区域 -->
    <view class="modules-section">
      <view class="modules-grid">
        <!-- 血糖记录 -->
        <view class="module-item" @click="goToBloodSugar">
          <view class="module-icon">
            <image src="/static/icons/blood-sugar.png" mode="aspectFit"></image>
          </view>
          <text class="module-title">血糖记录</text>
          <text class="module-desc">记录血糖数据</text>
        </view>

        <!-- 食物GI查询 -->
        <view class="module-item" @click="goToGIQuery">
          <view class="module-icon">
            <image src="/static/icons/gi-query.png" mode="aspectFit"></image>
          </view>
          <text class="module-title">食物GI查询</text>
          <text class="module-desc">查询食物升糖指数</text>
        </view>

        <!-- 运动控糖 -->
        <view class="module-item" @click="goToExercise">
          <view class="module-icon">
            <image src="/static/icons/exercise.png" mode="aspectFit"></image>
          </view>
          <text class="module-title">运动控糖</text>
          <text class="module-desc">运动健康管理</text>
        </view>

        <!-- 学习交流 -->
        <view class="module-item" @click="goToStudy">
          <view class="module-icon">
            <image src="/static/icons/study.png" mode="aspectFit"></image>
          </view>
          <text class="module-title">学习交流</text>
          <text class="module-desc">健康知识学习</text>
        </view>
      </view>
    </view>

    <!-- 快捷功能区域 -->
    <view class="quick-actions">
      <view class="section-title">快捷功能</view>
      <view class="action-list">
        <view class="action-item" @click="quickRecord">
          <text class="action-text">快速记录血糖</text>
          <text class="action-arrow">></text>
        </view>
        <view class="action-item" @click="todayData">
          <text class="action-text">今日数据统计</text>
          <text class="action-arrow">></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserInfo } from '@/utils/token.js'

export default {
  data() {
    return {
      userInfo: {}
    }
  },

  onShow() {
    this.loadUserInfo()
  },

  methods: {
    // 加载用户信息
    loadUserInfo() {
      this.userInfo = getUserInfo() || {}
    },

    // 跳转到血糖记录
    goToBloodSugar() {
      uni.navigateTo({
        url: '/pages/bloodSugar/record'
      })
    },

    // 跳转到GI查询
    goToGIQuery() {
      uni.navigateTo({
        url: '/pages/index/index'
      })
    },

    // 跳转到运动控糖
    goToExercise() {
      uni.navigateTo({
        url: '/pages/exercise/list'
      })
    },

    // 跳转到学习交流
    goToStudy() {
      uni.navigateTo({
        url: '/pages/study/list'
      })
    },

    // 快速记录血糖
    quickRecord() {
      uni.navigateTo({
        url: '/pages/bloodSugar/record'
      })
    },

    // 今日数据统计
    todayData() {
      uni.navigateTo({
        url: '/pages/bloodSugar/statistics'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header-section {
  position: relative;
  height: 300rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  overflow: hidden;

  .welcome-text {
    position: relative;
    z-index: 2;

    .greeting {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
      display: block;
      margin-bottom: 10rpx;
    }

    .subtitle {
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .header-bg {
    position: absolute;
    right: -50rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 200rpx;
    height: 200rpx;
    opacity: 0.3;
  }
}

.modules-section {
  padding: 40rpx;
  margin-top: -60rpx;
  position: relative;
  z-index: 3;

  .modules-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;

    .module-item {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 40rpx 30rpx;
      text-align: center;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .module-icon {
        width: 80rpx;
        height: 80rpx;
        margin: 0 auto 20rpx;
        background-color: #f0f2f5;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        image {
          width: 50rpx;
          height: 50rpx;
        }
      }

      .module-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 8rpx;
      }

      .module-desc {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.quick-actions {
  margin: 40rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .action-list {
    .action-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 25rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .action-text {
        font-size: 28rpx;
        color: #333;
      }

      .action-arrow {
        font-size: 24rpx;
        color: #ccc;
      }
    }
  }
}
</style>
