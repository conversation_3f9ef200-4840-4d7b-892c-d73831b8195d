import request from "@/utils/request";

export default {
    // 获取食物列表
    getFoodList(data) {
        return request({
            url: '/wechat/food/list',
            method: 'get',
            params: data,
			noToken: true,
        })
    },

    // 搜索食物
    searchFood(data) {
        return request({
            url: '/wechat/food/search',
            method: 'get',
            params: data,
			noToken: true,
        })
    },

    // 根据类别获取食物
    getFoodByCategory(data) {
        return request({
            url: '/wechat/food/category',
            method: 'get',
            params: data,
			noToken: true,
        })
    },

    // 获取食物种类列表
    getCategories() {
        return request({
            url: '/wechat/food/categories',
            method: 'get',
			noToken: true,
        })
    },

    // 根据升糖指数范围获取食物
    getFoodByGlycemicRange(data) {
        return request({
            url: '/wechat/food/glycemic',
            method: 'get',
            params: data,
			noToken: true,
        })
    },
} 