{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?39dc", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?8db6", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?f9fc", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?73cf", "uni-app:///pages/prevention/detail.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?df3f", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/prevention/detail.vue?5b01"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "detail", "onLoad", "newSrc", "tag"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAupB,CAAgB,wpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACQ3qB;AACA;;;;;;;;eACA;EACAC;IACA;MAAAC;IAAA;EACA;EACAC;IAAA;IACA;MACA;MACA;QACA,oDACA,6CACA;UACA;UACA;UACA;YACAC;UACA;UACA;UACA;UACA;UACAC;UACA;UACA,mBACA,8IAEA;QACA,EACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAAu9B,CAAgB,q7BAAG,EAAC,C;;;;;;;;;;;ACA3+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/prevention/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prevention/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=5a036632&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=5a036632&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5a036632\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prevention/detail.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=5a036632&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <view class=\"title\">{{ detail.title }}</view>\r\n    <video v-if=\"detail.type === '2' && detail.videoUrl\" :src=\"detail.videoUrl\" controls style=\"width:100%;margin:20rpx 0;\"/>\r\n    <rich-text class=\"content\" :nodes=\"detail.content\"></rich-text>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { getPreventionDetail } from './index.js'\r\nimport APP_CONFIG from '../../config/config.js'\r\nexport default {\r\n  data() {\r\n    return { detail: {} }\r\n  },\r\n onLoad(options) {\r\n  getPreventionDetail(options.id).then(res => {\r\n    this.detail = res.data || {}\r\n    if (this.detail.content) {\r\n      this.detail.content = this.detail.content.replace(\r\n        /<img([^>]*)src=['\"]([^'\"]+)['\"]([^>]*)>/gi,\r\n        (match, p1, src, p2) => {\r\n          // 1. 补全图片src为绝对路径\r\n          let newSrc = src;\r\n          if (!/^https?:\\/\\//.test(src)) {\r\n            newSrc = APP_CONFIG.VUE_APP_API_HOST_DEFAULT + (src.startsWith('/') ? src : '/' + src);\r\n          }\r\n          // 2. 强制添加内联样式，兼容小程序\r\n          // 先移除原有style\r\n          let tag = `<img${p1}src=\"${newSrc}\"${p2}>`;\r\n          tag = tag.replace(/style=['\\\"][^'\\\"]*['\\\"]/, '');\r\n          // 再加上新style（注意margin-left/right:auto和display:block）\r\n          return tag.replace(\r\n            /<img/,\r\n            `<img style=\"display:block;margin:20rpx auto 20rpx auto;max-width:100%;height:auto;clear:both;margin-left:auto;margin-right:auto;\"`\r\n          );\r\n        }\r\n      );\r\n    }\r\n  })\r\n},\r\n}\r\n</script>\r\n<style scoped>\r\n.container { padding: 24rpx; }\r\n.title { font-size: 36rpx; font-weight: bold; margin-bottom: 20rpx; text-align: center; }\r\n.cover { width: 100%; border-radius: 8rpx; margin-bottom: 20rpx; }\r\n.content { font-size: 30rpx; color: #222; }\r\n/* 让富文本图片单独成行并居中，图片后文字自动换行 */\r\n:deep(.content) img,\r\n>>> .content img,\r\n    /deep/ .content img {\r\n  display: block !important;\r\n  margin: 20rpx auto !important;\r\n  max-width: 100% !important;\r\n  height: auto !important;\r\n  clear: both;\r\n}\r\n</style>", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=5a036632&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=5a036632&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973583165\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}