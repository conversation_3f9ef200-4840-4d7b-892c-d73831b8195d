(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-meet-meet_reservation"],{"4fba":function(e,t,i){"use strict";var a=i("576f"),n=i.n(a);n.a},"576f":function(e,t,i){var a=i("80b2");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var n=i("4f06").default;n("57ddab2e",a,!0,{sourceMap:!1,shadowMode:!1})},"6f41":function(e,t,i){"use strict";i.r(t);var a=i("808d6"),n=i("8159");for(var o in n)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(o);i("4fba");var r=i("f0c5"),d=Object(r["a"])(n["default"],a["b"],a["c"],!1,null,"4ea13db0",null,!1,a["a"],void 0);t["default"]=d.exports},7684:function(e,t,i){"use strict";i("7a82");var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("3d6e")),o={submitMeetRecord:function(e){return(0,n.default)({url:"/app/meet/records",method:"post",data:e})},getMyRecordList:function(e){return(0,n.default)({url:"/app/meet/records/getMyList",method:"get",params:e})},getAllRecordList:function(e){return(0,n.default)({url:"/app/meet/records/getMeetCalender",method:"get",params:e})},getRoomRecordDetail:function(e){return(0,n.default)({url:"/app/meet/records/"+e,method:"get"})},cancelMeetRecord:function(e){return(0,n.default)({url:"/app/meet/records/cancel/"+e,method:"get"})},getMeetType:function(){return(0,n.default)({url:"/app/meet/records/getMeetType",method:"get"})},getMeetOccupy:function(e){return(0,n.default)({url:"/app/meet/records/getMeetOccupy",method:"post",data:e})},getMeetPeople:function(e){return(0,n.default)({url:"/app/meet/records/getMeetPeople/"+e,method:"get"})}};t.default=o},"808d6":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return o})),i.d(t,"a",(function(){return a}));var a={uCollapse:i("6acd").default,uCollapseItem:i("ee95").default},n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("v-uni-view",{staticClass:"time-table-wrapper"},[i("v-uni-view",{staticClass:"top-date"},[e._v(e._s(e.showTime))]),i("DateTabs",{on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.onDateTabsChange.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"meet_name"},[e._v(e._s(e.meetRoomDetail.meetRoomName))]),i("u-collapse",{attrs:{border:e.border}},[i("u-collapse-item",{attrs:{title:"选择上午时间",border:e.border}},[i("v-uni-view",{staticClass:"time-chose-view"},e._l(e.morningTimeList,(function(t,a){return i("v-uni-view",{key:t.index,staticClass:"time-item-view",class:{"occupy-style":t.occupy,"disabled-style":t.disabled,"chosed-style":t.chosed&&!t.disabled,"default-style":!t.chosed&&!t.disabled},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.choseTime(t.index)}}},[e._v(e._s(t.name))])})),1)],1)],1),i("u-collapse",{attrs:{border:e.border}},[i("u-collapse-item",{attrs:{title:"选择下午时间",border:e.border}},[i("v-uni-view",{staticClass:"time-chose-view"},e._l(e.afternoonTimeList,(function(t,a){return i("v-uni-view",{key:t.index,staticClass:"time-item-view",class:{"occupy-style":t.occupy,"disabled-style":t.disabled,"chosed-style":t.chosed&&!t.disabled,"default-style":!t.chosed&&!t.disabled},on:{click:function(i){arguments[0]=i=e.$handleEvent(i),e.choseTime(t.index)}}},[e._v(e._s(t.name))])})),1)],1)],1),i("v-uni-view",{staticStyle:{height:"200rpx"}}),e.startTime&&e.endTime?i("v-uni-view",{staticClass:"time-submit",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.bindSubmitTap.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"inner"},[e._v("下一步 ("+e._s(e.startTime.substring(11))+"~"+e._s(e.endTime.substring(11))+")")])],1):e._e()],1)},o=[]},"80b2":function(e,t,i){var a=i("24fb");t=a(!1),t.push([e.i,".time-table-wrapper[data-v-4ea13db0]{display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative}.top-date[data-v-4ea13db0]{box-sizing:border-box;width:100%;padding-left:%?25?%;padding-top:%?10?%;padding-bottom:%?10?%;font-weight:600}.meet_name[data-v-4ea13db0]{width:100%;text-align:center;margin-top:%?20?%;font-weight:600}.time-title[data-v-4ea13db0]{width:100%;padding-left:%?50?%;font-size:%?25?%;margin-top:%?20?%}.time-chose-view[data-v-4ea13db0]{display:flex;flex-wrap:wrap;\n\t/* 允许子元素换行 */justify-content:space-between;\n\t/* 子元素之间的间距 */box-sizing:border-box;width:100%;padding-left:%?25?%;padding-right:%?25?%;margin-top:%?20?%}.time-item-view[data-v-4ea13db0]{margin-bottom:%?20?%;height:%?50?%;\n\t/* width: 800rpx; */flex-basis:calc(30% - %?10?%);border-radius:%?15?%;text-align:center;line-height:%?50?%;font-size:%?25?%}.default-style[data-v-4ea13db0]{border:%?3?% solid #d8d5e2;color:#007aff}.disabled-style[data-v-4ea13db0]{border:%?3?% solid #d8d5e2;background-color:#e2e2e2}.occupy-style[data-v-4ea13db0]{border:%?3?% solid #d8d5e2;background-color:#8669f5;color:#fff}.chosed-style[data-v-4ea13db0]{color:#fff;background-color:#007aff}.time-submit[data-v-4ea13db0]{width:90%;position:fixed;bottom:%?10?%;z-index:999999}.time-submit .inner[data-v-4ea13db0]{width:100%;background-color:#007aff;font-size:%?35?%;border-radius:%?15?%;padding:%?10?% 0;text-align:center;color:#fff}.meet-occupy[data-v-4ea13db0]{width:80%;height:%?500?%;background-color:#eac87f;position:fixed;bottom:30%;z-index:999999}",""]),e.exports=t},8159:function(e,t,i){"use strict";i.r(t);var a=i("8417"),n=i.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(o);t["default"]=n.a},8417:function(e,t,i){"use strict";i("7a82");var a=i("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(i("ade3")),o=a(i("b85c"));i("4de4"),i("d3b7"),i("e25e"),i("159b"),i("99af"),i("14d9"),i("4d90");var r=a(i("7684")),d=a(i("d7c3")),s=a(i("d344")),c=a(i("650f")),l={components:{DateTabs:s.default},onLoad:function(e){var t=e.roomId;this.getMeetRoomDetail(t),this.showTime=c.default.time("Y年M月D日"),this.day=c.default.time("Y-M-D");var i=uni.getStorageSync("meetInfo"),a=i.id;this.initializeTimeSelectList(t,this.day,a)},data:function(){return{meetRoomDetail:{},selectedItemIndex:null,startTime:"",endTime:"",border:!1,showTime:"",day:"",timeSelectList:[],morningTimeList:[],afternoonTimeList:[]}},onShow:function(){},methods:{getMeetRoomDetail:function(e){var t=this;d.default.getRoomDetail(e).then((function(e){200==e.code&&(t.meetRoomDetail=e.data)}))},choseTime:function(e){var t=this.timeSelectList,i=t[e];if(!i.disabled)if(i.occupy){var a=i.meetRecordId;uni.showModal({title:"提示：",content:"该时间已被占用，是否查看会议详情?",success:function(e){e.confirm?uni.navigateTo({url:"/pages/meet/meet_detail?id="+a}):e.cancel}})}else{var n=i.startTimeStr,o=i.endTimeStr;if(null===this.selectedItemIndex)this.selectedItemIndex=e,this.resetChosed(),i.chosed=!0,this.startTime=n,this.endTime=o;else{var r=this.selectedItemIndex;t[r];if(r===e)this.resetChosed(),this.startTime="",this.endTime="",this.selectedItemIndex=null;else{var d=[Math.min(r,e),Math.max(r,e)],s=d[0],c=d[1];this.resetChosed();for(var l=s;l<=c;l++){if(t[l].occupy)return this.resetChosed(),this.startTime="",this.endTime="",void(this.selectedItemIndex=null);t[l].chosed=!0}this.startTime=t[s].startTimeStr,this.endTime=t[c].endTimeStr,this.selectedItemIndex=e}}this.morningTimeList=t.filter((function(e){var t=parseInt(e.startTime.split(":")[0],10);return t<12})),this.afternoonTimeList=t.filter((function(e){var t=parseInt(e.startTime.split(":")[0],10);return t>=12}))}},resetChosed:function(){this.timeSelectList.forEach((function(e){return e.chosed=!1}))},onDateTabsChange:function(e){var t=c.default.fmtDateCHN(e.dd,"Y年M月D日"),i=c.default.fmtDateCHN(e.dd,"Y-M-D");this.day=i,this.showTime=t,this.initializeTimeSelectList(),this.$forceUpdate()},initializeTimeSelectList:function(e,t,i){var a=this,d=[];console.log("recordId:",i),r.default.getMeetOccupy({meetRoomId:e,day:t,meetRecordId:i}).then((function(e){200==e.code&&(d=e.data);var t=new Date,i=a.day;a.timeSelectList=[];for(var r=0,s=0;s<=23;s++)for(var c=0;c<60;c+=15){var l,u="".concat(i," ").concat(a.formatTime(s,c)),f=new Date(u),m=new Date(f);m.setMinutes(f.getMinutes()+15);var h="".concat(i," ").concat(a.formatTime(m.getHours(),m.getMinutes()));if(m.getDate()!==f.getDate()){var v=new Date(i);v.setDate(v.getDate()+1);var p=v.toISOString().split("T")[0];h="".concat(p," ").concat(a.formatTime(m.getHours(),m.getMinutes()))}var b,g=!1,T="",y=(0,o.default)(d);try{for(y.s();!(b=y.n()).done;){var w=b.value,x=new Date(w.startTime),S=new Date(w.endTime);if(f>=x&&f<S||m>x&&m<=S||f<=x&&m>=S){g=!0,T=w.meetRecordId;break}}}catch(I){y.e(I)}finally{y.f()}var M=(l={index:r++,name:"".concat(a.formatTime(s,c),"-").concat(a.formatTime(m.getHours(),m.getMinutes())),startTime:a.formatTime(s,c),endTime:a.formatTime(m.getHours(),m.getMinutes()),startTimeStr:u,endTimeStr:h,chosed:!1,disabled:m<=t,currentDate:t},(0,n.default)(l,"endTime",m),(0,n.default)(l,"occupy",g),(0,n.default)(l,"meetRecordId",T),l);if(a.timeSelectList.push(M),23===s&&c+15===60)break}a.morningTimeList=a.timeSelectList.filter((function(e){var t=parseInt(e.startTime.split(":")[0],10);return t<12})),a.afternoonTimeList=a.timeSelectList.filter((function(e){var t=parseInt(e.startTime.split(":")[0],10);return t>=12})),a.$forceUpdate()}))},formatTime:function(e,t){var i=String(e).padStart(2,"0"),a=String(t).padStart(2,"0");return"".concat(i,":").concat(a)},bindSubmitTap:function(){console.log("startTime:",this.startTime),console.log("endTime:",this.endTime);var e=uni.getStorageSync("meetInfo");console.log("meet:",e),e.startTime=this.startTime,e.endTime=this.endTime,uni.setStorageSync("meetInfo",e),uni.navigateTo({url:"/pages/meet/meet_reservation_submit"})}}};t.default=l},b85c:function(e,t,i){"use strict";i("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var i="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=(0,a.default)(e))||t&&e&&"number"===typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,d=!0,s=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return d=e.done,e},e:function(e){s=!0,r=e},f:function(){try{d||null==i["return"]||i["return"]()}finally{if(s)throw r}}}},i("a4d3"),i("e01a"),i("d3b7"),i("d28b"),i("3ca3"),i("ddb0"),i("d9e2"),i("d401");var a=function(e){return e&&e.__esModule?e:{default:e}}(i("06c5"))}}]);