<template>
	<view class="my-container">
		<view class="header">
			<view class="user-info">
				<image class="avatar" src="/static/logo.png" mode="aspectFit"></image>
				<view class="user-details">
					<text class="username">{{userInfo.userName || '未登录'}}</text>
					<text class="user-desc">{{getUserDesc()}}</text>
				</view>
			</view>
		</view>
		
		<view class="content">
			<view class="info-section">
				<view class="section-title">个人信息</view>
				<view class="info-item">
					<text class="label">性别：</text>
					<text class="value">{{userInfo.gender === '0' ? '男' : '女'}}</text>
				</view>
				<view class="info-item">
					<text class="label">年龄：</text>
					<text class="value">{{userInfo.age}}岁</text>
				</view>
				<view class="info-item">
					<text class="label">身高：</text>
					<text class="value">{{userInfo.height}}cm</text>
				</view>
				<view class="info-item">
					<text class="label">体重：</text>
					<text class="value">{{userInfo.weight}}kg</text>
				</view>
				<view class="info-item" v-if="userInfo.phone">
					<text class="label">联系方式：</text>
					<text class="value">{{userInfo.phone}}</text>
				</view>
				<view class="info-item">
					<text class="label">患病时长：</text>
					<text class="value">{{getDiabetesText()}}</text>
				</view>
			</view>
			
			<view class="action-section">
				<button class="action-btn" @click="editProfile">编辑资料</button>
				<button class="action-btn" @click="goToPost">随手拍</button>
				<button class="logout-btn" @click="handleLogout">退出登录</button>
			</view>
		</view>
	</view>
</template>

<script>
	import { getUserInfo, setUserInfo, clearAll } from '@/utils/token.js'
	import request from '@/utils/request.js'
	
	export default {
		data() {
			return {
				userInfo: {},
				userId: '' // 新增：用于存储openid
			}
		},
		
		onShow() {
			this.loadUserInfo()
		},
		
		methods: {
			// 加载用户信息
			async loadUserInfo() {
				const openid = uni.getStorageSync('openid');
				if (!openid) {
					uni.reLaunch({ url: '/pages/login/login' });
					return;
				}
				this.userId = openid; // 设置userId
				try {
					const res = await request({
						url: '/wechat/user/getUserInfo',
						method: 'post',
						data: { openid } // 将 params 改为 data
					});
					if (res.code === 200 && res.data) {
						this.userInfo = res.data;
						setUserInfo(res.data); // 更新本地存储的用户信息
					} else {
						uni.showToast({ title: '获取用户信息失败', icon: 'none' });
					}
				} catch (e) {
					uni.showToast({ title: '网络错误', icon: 'none' });
				}
			},
			
			// 获取用户描述
						getUserDesc() {
							if (!this.userInfo.userName) return ''
							return `注册时间：${this.formatDate(this.userInfo.registerTime)}`
						},
						
						// 获取患病时长文本
						getDiabetesText() {
							const years = this.userInfo.diabetesYears || 0
							if (years === 0) return '无'
							if (years >= 10) return '10年以上'
							return `${years}年`
						},
						
						// 格式化日期
						formatDate(dateString) {
							if (!dateString) return ''
							const date = new Date(dateString)
							return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
						},
						
						// 编辑资料
						editProfile() {
							uni.navigateTo({ url: '/pages/my/editProfile' });
						},
						
						// 退出登录
						handleLogout() {
							uni.showModal({
								title: '提示',
								content: '确定要退出登录吗？',
								success: (res) => {
									if (res.confirm) {
										// 清除本地存储
										clearAll()
										
										// 跳转到登录页面
										uni.reLaunch({
											url: '/pages/login/login'
										})
									}
								}
							})
						},
			
						// 跳转到随手拍页面
						goToPost() {
							uni.navigateTo({ url: '/pages/post/post' });
						}
			
		}
	}
</script>

<style lang="scss" scoped>
	.my-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	.header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 60rpx 40rpx 40rpx;
		
		.user-info {
			display: flex;
			align-items: center;
			
			.avatar {
				width: 120rpx;
				height: 120rpx;
				border-radius: 60rpx;
				margin-right: 30rpx;
				background-color: rgba(255, 255, 255, 0.2);
			}
			
			.user-details {
				flex: 1;
				
				.username {
					font-size: 36rpx;
					font-weight: bold;
					color: #fff;
					display: block;
					margin-bottom: 10rpx;
				}
				
				.user-desc {
					font-size: 26rpx;
					color: rgba(255, 255, 255, 0.8);
				}
			}
		}
	}
	
	.content {
		padding: 40rpx;
		
		.info-section {
			background-color: #fff;
			border-radius: 20rpx;
			padding: 40rpx;
			margin-bottom: 40rpx;
			
			.section-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 30rpx;
			}
			
			.info-item {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.label {
					font-size: 28rpx;
					color: #666;
					width: 160rpx;
				}
				
				.value {
					font-size: 28rpx;
					color: #333;
					flex: 1;
				}
			}
		}
		
		.action-section {
			.action-btn {
				width: 100%;
				height: 88rpx;
				background-color: #667eea;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				border: none;
				margin-bottom: 20rpx;
			}
			
			.logout-btn {
				width: 100%;
				height: 88rpx;
				background-color: #ff4757;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				border: none;
			}
		}
	}
</style>
