<template>
  <view class="edit-profile-container">
    <view class="form-section">
      <view class="form-item">
        <text class="label">性别</text>
        <picker :range="genderOptions" v-model="form.gender">
          <view class="picker-value">{{ genderOptions[form.gender] }}</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="label">年龄</text>
        <input class="input" type="number" v-model="form.age" placeholder="请输入年龄" />
      </view>
      <view class="form-item">
        <text class="label">身高(cm)</text>
        <input class="input" type="number" v-model="form.height" placeholder="请输入身高" />
      </view>
      <view class="form-item">
        <text class="label">体重(kg)</text>
        <input class="input" type="number" v-model="form.weight" placeholder="请输入体重" />
      </view>
      <view class="form-item">
        <text class="label">联系方式</text>
        <input class="input" type="text" v-model="form.phone" placeholder="请输入联系方式" />
      </view>
      <view class="form-item">
        <text class="label">患病时长(年)</text>
        <input class="input" type="number" v-model="form.diabetesYears" placeholder="请输入患病时长" />
      </view>
    </view>
    <button class="save-btn" @click="submitForm">保存</button>
  </view>
</template>

<script>
import { getUserInfo, setUserInfo } from '@/utils/token.js'
import request from '@/utils/request.js'

export default {
  data() {
    return {
      form: {
        gender: 0,
        age: '',
        height: '',
        weight: '',
        phone: '',
        diabetesYears: ''
      },
      genderOptions: ['男', '女']
    }
  },
  onLoad() {
    const userInfo = getUserInfo() || {}
    this.form = {
      userId: userInfo.userId, // 新增userId字段
      gender: userInfo.gender || 0,
      age: userInfo.age || '',
      height: userInfo.height || '',
      weight: userInfo.weight || '',
      phone: userInfo.phone || '',
      diabetesYears: userInfo.diabetesYears || ''
    }
  },
  methods: {
    submitForm() {
      // 简单校验
      if (!this.form.age || !this.form.height || !this.form.weight) {
        uni.showToast({ title: '请填写完整信息', icon: 'none' })
        return
      }
      // 调用后端API保存
      request({
        url: '/wechat/user/profile',
        method: 'put',
        data: this.form
      }).then(res => {
        if (res.code === 200) {
          // 更新本地缓存
          setUserInfo({ ...getUserInfo(), ...this.form })
          uni.showToast({ title: '保存成功', icon: 'success' })
          setTimeout(() => {
            uni.navigateBack()
          }, 800)
        } else {
          uni.showToast({ title: res.msg || '保存失败', icon: 'none' })
        }
      })
    }
  }
}
</script>

<style scoped>
.edit-profile-container { padding: 40rpx; }
.form-section { background: #fff; border-radius: 20rpx; padding: 30rpx; margin-bottom: 40rpx; }
.form-item { display: flex; align-items: center; margin-bottom: 30rpx; }
.label { width: 160rpx; color: #666; font-size: 28rpx; }
.input, .picker-value { flex: 1; border: 1px solid #eee; border-radius: 8rpx; padding: 16rpx; font-size: 28rpx; }
.save-btn { width: 100%; height: 88rpx; background: #667eea; color: #fff; font-size: 32rpx; border-radius: 44rpx; border: none; }
</style> 