<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d40f5d64-39aa-4e37-adf6-fccae6ac29b2" name="Changes" comment="xiugaitijiao" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../../maven/apache-maven-3.6.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zm4tUkjaga25kbGaCyro9QGufa" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.ruoyi [clean].executor": "Run",
    "Maven.ruoyi [compile].executor": "Run",
    "Maven.ruoyi [install].executor": "Run",
    "MyBatisLogPluginindexNum": "1032",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.RuoYiApplication.executor": "Run",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Codes/transport-server",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Problems",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\code\糖尿病小程序\GlucoCare-admin\ruoyi-admin\src\main\resources" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.ruoyi.common.config" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="RuoYiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="ruoyi-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ruoyi.RuoYiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-28b599e66164-intellij.indexing.shared.core-IU-242.23339.11" />
        <option value="bundled-js-predefined-d6986cc7102b-5c90d61e3bab-JavaScript-IU-242.23339.11" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d40f5d64-39aa-4e37-adf6-fccae6ac29b2" name="Changes" comment="" />
      <created>1752323133538</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752323133538</updated>
      <workItem from="1752323135450" duration="5607000" />
      <workItem from="1752637198783" duration="1885000" />
      <workItem from="1752640338846" duration="357000" />
      <workItem from="1752642256319" duration="38850000" />
      <workItem from="1752828389923" duration="1842000" />
      <workItem from="1753061191344" duration="2853000" />
      <workItem from="1753067682424" duration="2314000" />
      <workItem from="1753076665517" duration="6215000" />
      <workItem from="1753092112195" duration="7808000" />
      <workItem from="1753669516335" duration="1521000" />
      <workItem from="1753759031057" duration="135000" />
      <workItem from="1753770685637" duration="3097000" />
      <workItem from="1753775224415" duration="3872000" />
      <workItem from="1753944005431" duration="1352000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>