<template>
<view class="x-pdf" v-if="visible">
  <u-popup  v-model="visible"
            :closeable="true"
            duration="0"
            :height="height"
            mode="bottom">
    <view style="overflow: hidden">
      <web-view :src="src"></web-view>
    </view>
  </u-popup>
</view>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      height: '100%'
    };
  },
  computed: {
    src () {
      if (!this.url) return '';
      return '/hybrid/html/pdf/web/viewer.html?file=' + encodeURIComponent(this.url);
    }
  },
  mounted() {
    document.body.append(this.$el);
  },
  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el);
    }
  }
};
</script>

<style scoped lang="scss">
.x-pdf{
  height: 100vh;
  overflow: scroll;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;

  ::v-deep .uni-scroll-view-content{
    background: #4a4a4a;
  }
}
</style>
