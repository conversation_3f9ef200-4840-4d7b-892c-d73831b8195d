import APP_CONFIG from "@/config/config";
import { getToken, removeToken } from '@/utils/token.js';

var baseUrl = APP_CONFIG.VUE_APP_API_HOST_DEFAULT;

const uploadFileApi = (filePath) => {
  return new Promise((resolve, reject) => {
    let token = getToken();
    if (!token) {
      reject('未找到 token，请先登录');
      return;
    }
    let header = { 'Authorization': 'Bearer ' + token };
    uni.uploadFile({
      url: baseUrl + "/common/upload",
      filePath: filePath,
      name: 'file',
      header: header,
      formData: { 'file': 'file' },
      success: (uploadFileRes) => {
        try {
          const parsedData = JSON.parse(uploadFileRes.data);
          if (parsedData.code === 401 || parsedData.code === "115") {
            removeToken();
            uni.reLaunch({ url: '/pages/login/login' });
            reject('登录失效，请重新登录');
            return;
          }
          resolve(parsedData);
        } catch (error) {
          reject(`解析响应数据失败: ${error.message}`);
        }
      },
      fail: (err) => {
        console.error('上传失败：', err);
        reject(err);
      }
    });
  });
};

export default {
  uploadImage: uploadFileApi
};