{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>", "uni-app:///pages/home/<USER>", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/home/<USER>"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "onShow", "methods", "loadUserInfo", "goToBloodSugar", "uni", "url", "goToGIQuery", "goToExercise", "goToStudy", "quickRecord", "todayData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqpB,CAAgB,spBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACsEzqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACAJ;QACAC;MACA;IACA;IAEA;IACAI;MACAL;QACAC;MACA;IACA;IAEA;IACAK;MACAN;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnIA;AAAA;AAAA;AAAA;AAAowC,CAAgB,6rCAAG,EAAC,C;;;;;;;;;;;ACAxxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/home/<USER>", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/home/<USER>'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"\nvar renderjs\nimport script from \"./home.vue?vue&type=script&lang=js&\"\nexport * from \"./home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"92bb8f34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/home/<USER>\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./home.vue?vue&type=template&id=92bb8f34&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./home.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"home-container\">\n    <!-- 顶部欢迎区域 -->\n    <view class=\"header-section\">\n      <view class=\"welcome-text\">\n        <text class=\"greeting\">您好，{{userInfo.userName || '用户'}}</text>\n        <text class=\"subtitle\">欢迎使用GlucoCare血糖管理助手</text>\n      </view>\n      <image class=\"header-bg\" src=\"/static/home-bg.png\" mode=\"aspectFill\"></image>\n    </view>\n\n    <!-- 功能模块区域 -->\n    <view class=\"modules-section\">\n      <view class=\"modules-grid\">\n        <!-- 血糖记录 -->\n        <view class=\"module-item\" @click=\"goToBloodSugar\">\n          <view class=\"module-icon\">\n            <image src=\"/static/icons/blood-sugar.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"module-title\">血糖记录</text>\n          <text class=\"module-desc\">记录血糖数据</text>\n        </view>\n\n        <!-- 食物GI查询 -->\n        <view class=\"module-item\" @click=\"goToGIQuery\">\n          <view class=\"module-icon\">\n            <image src=\"/static/icons/gi-query.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"module-title\">食物GI查询</text>\n          <text class=\"module-desc\">查询食物升糖指数</text>\n        </view>\n\n        <!-- 运动控糖 -->\n        <view class=\"module-item\" @click=\"goToExercise\">\n          <view class=\"module-icon\">\n            <image src=\"/static/icons/exercise.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"module-title\">运动控糖</text>\n          <text class=\"module-desc\">运动健康管理</text>\n        </view>\n\n        <!-- 学习交流 -->\n        <view class=\"module-item\" @click=\"goToStudy\">\n          <view class=\"module-icon\">\n            <image src=\"/static/icons/study.png\" mode=\"aspectFit\"></image>\n          </view>\n          <text class=\"module-title\">学习交流</text>\n          <text class=\"module-desc\">健康知识学习</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快捷功能区域 -->\n    <view class=\"quick-actions\">\n      <view class=\"section-title\">快捷功能</view>\n      <view class=\"action-list\">\n        <view class=\"action-item\" @click=\"quickRecord\">\n          <text class=\"action-text\">快速记录血糖</text>\n          <text class=\"action-arrow\">></text>\n        </view>\n        <view class=\"action-item\" @click=\"todayData\">\n          <text class=\"action-text\">今日数据统计</text>\n          <text class=\"action-arrow\">></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getUserInfo } from '@/utils/token.js'\n\nexport default {\n  data() {\n    return {\n      userInfo: {}\n    }\n  },\n\n  onShow() {\n    this.loadUserInfo()\n  },\n\n  methods: {\n    // 加载用户信息\n    loadUserInfo() {\n      this.userInfo = getUserInfo() || {}\n    },\n\n    // 跳转到血糖记录\n    goToBloodSugar() {\n      uni.navigateTo({\n        url: '/pages/bloodSugar/record'\n      })\n    },\n\n    // 跳转到GI查询\n    goToGIQuery() {\n      uni.navigateTo({\n        url: '/pages/index/index'\n      })\n    },\n\n    // 跳转到运动控糖\n    goToExercise() {\n      uni.navigateTo({\n        url: '/pages/exercise/list'\n      })\n    },\n\n    // 跳转到学习交流\n    goToStudy() {\n      uni.navigateTo({\n        url: '/pages/study/list'\n      })\n    },\n\n    // 快速记录血糖\n    quickRecord() {\n      uni.navigateTo({\n        url: '/pages/bloodSugar/record'\n      })\n    },\n\n    // 今日数据统计\n    todayData() {\n      uni.navigateTo({\n        url: '/pages/bloodSugar/statistics'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.home-container {\n  min-height: 100vh;\n  background-color: #f5f7fa;\n}\n\n.header-section {\n  position: relative;\n  height: 300rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 60rpx 40rpx 40rpx;\n  overflow: hidden;\n\n  .welcome-text {\n    position: relative;\n    z-index: 2;\n\n    .greeting {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n      display: block;\n      margin-bottom: 10rpx;\n    }\n\n    .subtitle {\n      font-size: 26rpx;\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n\n  .header-bg {\n    position: absolute;\n    right: -50rpx;\n    top: 50%;\n    transform: translateY(-50%);\n    width: 200rpx;\n    height: 200rpx;\n    opacity: 0.3;\n  }\n}\n\n.modules-section {\n  padding: 40rpx;\n  margin-top: -60rpx;\n  position: relative;\n  z-index: 3;\n\n  .modules-grid {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 30rpx;\n\n    .module-item {\n      background-color: #fff;\n      border-radius: 20rpx;\n      padding: 40rpx 30rpx;\n      text-align: center;\n      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n      transition: transform 0.2s ease;\n\n      &:active {\n        transform: scale(0.95);\n      }\n\n      .module-icon {\n        width: 80rpx;\n        height: 80rpx;\n        margin: 0 auto 20rpx;\n        background-color: #f0f2f5;\n        border-radius: 40rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n\n        image {\n          width: 50rpx;\n          height: 50rpx;\n        }\n      }\n\n      .module-title {\n        font-size: 30rpx;\n        font-weight: bold;\n        color: #333;\n        display: block;\n        margin-bottom: 8rpx;\n      }\n\n      .module-desc {\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n  }\n}\n\n.quick-actions {\n  margin: 40rpx;\n  background-color: #fff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n\n  .section-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 30rpx;\n  }\n\n  .action-list {\n    .action-item {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 25rpx 0;\n      border-bottom: 1rpx solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .action-text {\n        font-size: 28rpx;\n        color: #333;\n      }\n\n      .action-arrow {\n        font-size: 24rpx;\n        color: #ccc;\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./home.vue?vue&type=style&index=0&id=92bb8f34&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973584497\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}