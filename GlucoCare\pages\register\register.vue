<template>
	<view class="register-container">
		<view class="header">
			<text class="title">用户注册</text>
			<text class="subtitle">请完善您的个人信息</text>
		</view>
		
		<view class="form-container">
			<view class="form-item">
				<text class="label">姓名 <text class="required">*</text></text>
				<input class="input" v-model="form.userName" placeholder="请输入您的姓名" />
			</view>
			
			<view class="form-item">
				<text class="label">性别 <text class="required">*</text></text>
				<view class="radio-group">
					<label class="radio-item">
						<radio value="0" :checked="form.gender === '0'" @click="selectGender('0')" />
						<text>男</text>
					</label>
					<label class="radio-item">
						<radio value="1" :checked="form.gender === '1'" @click="selectGender('1')" />
						<text>女</text>
					</label>
				</view>
			</view>
			
			<view class="form-item">
				<text class="label">年龄 <text class="required">*</text></text>
				<input class="input" v-model="form.age" type="number" placeholder="请输入您的年龄" />
			</view>
			
			<view class="form-item">
				<text class="label">身高(cm) <text class="required">*</text></text>
				<input class="input" v-model="form.height" type="digit" placeholder="请输入您的身高" />
			</view>
			
			<view class="form-item">
				<text class="label">体重(kg) <text class="required">*</text></text>
				<input class="input" v-model="form.weight" type="digit" placeholder="请输入您的体重" />
			</view>
			
			<view class="form-item">
				<text class="label">联系方式</text>
				<input class="input" v-model="form.phone" placeholder="请输入您的联系方式" />
			</view>
			
			<view class="form-item">
				<text class="label">患糖尿病时长</text>
				<picker mode="selector" :range="diabetesOptions" @change="onDiabetesChange">
					<view class="picker-input">
						<text class="picker-text">{{diabetesText}}</text>
						<text class="picker-arrow">></text>
					</view>
				</picker>
			</view>
		</view>
		
		<view class="button-container">
			<button class="register-btn" @click="handleRegister" :disabled="loading">
				{{loading ? '注册中...' : '注册'}}
			</button>
		</view>
	</view>
</template>

<script>
	import { registerUser } from '@/api/user/user.js'
	
	export default {
		data() {
			return {
				loading: false,
				form: {
					userName: '',
					gender: '',
					age: '',
					height: '',
					weight: '',
					phone: '',
					diabetesYears: 0
				},
				diabetesOptions: ['无', '1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年', '10年以上'],
				diabetesText: '无'
			}
		},
		methods: {
			// 选择性别
			selectGender(gender) {
				this.form.gender = gender
			},
			
			// 选择患病时长
			onDiabetesChange(e) {
				const index = e.detail.value
				this.diabetesText = this.diabetesOptions[index]
				this.form.diabetesYears = index
			},
			
			// 表单验证
			validateForm() {
				if (!this.form.userName.trim()) {
					uni.showToast({
						title: '请输入姓名',
						icon: 'none'
					})
					return false
				}
				
				if (!this.form.gender) {
					uni.showToast({
						title: '请选择性别',
						icon: 'none'
					})
					return false
				}
				
				if (!this.form.age || this.form.age <= 0) {
					uni.showToast({
						title: '请输入有效年龄',
						icon: 'none'
					})
					return false
				}
				
				if (!this.form.height || this.form.height <= 0) {
					uni.showToast({
						title: '请输入有效身高',
						icon: 'none'
					})
					return false
				}
				
				if (!this.form.weight || this.form.weight <= 0) {
					uni.showToast({
						title: '请输入有效体重',
						icon: 'none'
					})
					return false
				}
				
				return true
			},
			
			// 获取微信授权码
			getWechatCode() {
				return new Promise((resolve, reject) => {
					uni.login({
						provider: 'weixin',
						success: (res) => {
							if (res.code) {
								resolve(res.code)
							} else {
								reject(new Error('获取授权码失败'))
							}
						},
						fail: (err) => {
							reject(err)
						}
					})
				})
			},
			
			// 处理注册
			async handleRegister() {
				if (!this.validateForm()) {
					return
				}
				
				this.loading = true
				
				try {
					// 获取微信授权码
					const code = await this.getWechatCode()
					
					// 构造注册数据
					const registerData = {
						code: code,
						userName: this.form.userName,
						gender: this.form.gender,
						age: parseInt(this.form.age),
						height: parseFloat(this.form.height),
						weight: parseFloat(this.form.weight),
						phone: this.form.phone,
						diabetesYears: this.form.diabetesYears
					}
					
					// 调用注册接口
					const response = await registerUser(registerData)
					
					if (response.code === 200) {
						uni.showToast({
							title: '注册成功',
							icon: 'success'
						})
						
						// 延迟跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							})
						}, 1500)
					} else {
						uni.showToast({
							title: response.msg || '注册失败',
							icon: 'none'
						})
					}
					
				} catch (error) {
					console.error('注册失败:', error)
					uni.showToast({
						title: '注册失败，请重试',
						icon: 'none'
					})
				} finally {
					this.loading = false
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.register-container {
		padding: 40rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.title {
			font-size: 48rpx;
			font-weight: bold;
			color: #333;
			display: block;
			margin-bottom: 20rpx;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: #666;
		}
	}
	
	.form-container {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 60rpx;
	}
	
	.form-item {
		margin-bottom: 40rpx;
		
		.label {
			font-size: 32rpx;
			color: #333;
			display: block;
			margin-bottom: 20rpx;
			
			.required {
				color: #ff4757;
			}
		}
		
		.input {
			width: 100%;
			height: 80rpx;
			border: 2rpx solid #e1e1e1;
			border-radius: 10rpx;
			padding: 0 20rpx;
			font-size: 30rpx;
			background-color: #fff;
			
			&:focus {
				border-color: #007aff;
			}
		}
		
		.radio-group {
			display: flex;
			gap: 40rpx;
			
			.radio-item {
				display: flex;
				align-items: center;
				gap: 10rpx;
				font-size: 30rpx;
				color: #333;
			}
		}
		
		.picker-input {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			border: 2rpx solid #e1e1e1;
			border-radius: 10rpx;
			padding: 0 20rpx;
			background-color: #fff;
			
			.picker-text {
				font-size: 30rpx;
				color: #333;
			}
			
			.picker-arrow {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
	
	.button-container {
		.register-btn {
			width: 100%;
			height: 88rpx;
			background-color: #007aff;
			color: #fff;
			font-size: 32rpx;
			border-radius: 44rpx;
			border: none;
			
			&:disabled {
				background-color: #ccc;
			}
		}
	}
</style>
