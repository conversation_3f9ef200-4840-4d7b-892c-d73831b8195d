{"id": "statistic-week", "displayName": "仿钉钉考勤统计周历，支持左右滑动切换月份，快速选择月份，每个月有多少周", "version": "1.0.2", "description": "仿钉钉日历周历组件 支持左右滑动切换月份，picker快速选择月份，每个月有多少周，今天在本月的第几周，支持在APP、小程序、H5运行", "keywords": ["日历", "周历", "统计", "统计日历"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "y", "app-nvue": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "u", "联盟": "u"}}}}}