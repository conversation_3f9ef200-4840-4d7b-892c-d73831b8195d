{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?46ff", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?921a", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?3211", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?073a", "uni-app:///pages/login/login.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?13d0", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/login/login.vue?e847"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "methods", "getWechatCode", "uni", "provider", "success", "resolve", "reject", "fail", "handleLogin", "code", "response", "userInfo", "token", "title", "icon", "setTimeout", "url", "content", "console", "goToRegister", "checkLoginStatus", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAspB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACiC1qB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;YACA;cACAC;YACA;cACAC;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAC;gBAAA;gBAAA,OAGA;kBAAAA;gBAAA;cAAA;gBAAAC;gBAEA;kBACA;kBACAC;kBACAC,6DAEA;kBACA;kBACA;kBACA;oBACAV;kBACA;kBAEAA;oBACAW;oBACAC;kBACA;;kBAEA;kBACAC;oBACAb;sBACAc;oBACA;kBACA;gBAEA;kBACA;kBACA;oBACAd;sBACAW;sBACAI;sBACAb;wBACA;0BACA;wBACA;sBACA;oBACA;kBACA;oBACAF;sBACAW;sBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAI;gBACAhB;kBACAW;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAK;MACAjB;QACAc;MACA;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAX;gBAAA;gBAAA,OACA;kBAAAA;gBAAA;cAAA;gBAAAC;gBAEA;kBACA;kBACAC;kBACAC;kBACA;kBACA;kBAEAV;oBACAc;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EAEAG;IACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAAqwC,CAAgB,8rCAAG,EAAC,C;;;;;;;;;;;ACAzxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"login-container\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\"></image>\r\n\t\t\t<text class=\"title\">控糖智汇</text>\r\n\t\t\t<text class=\"subtitle\">糖尿病知识相关科普</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"login-content\">\r\n\t\t\t<view class=\"welcome-text\">\r\n\t\t\t\t<text class=\"welcome-title\">欢迎使用</text>\r\n\t\t\t\t<text class=\"welcome-desc\">请使用微信账号登录</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"button-container\">\r\n\t\t\t\t<button class=\"login-btn\" @click=\"handleLogin\" :disabled=\"loading\">\r\n\t\t\t\t\t<text class=\"btn-text\">{{loading ? '登录中...' : '微信登录'}}</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"register-link\">\r\n\t\t\t\t\t<text class=\"register-text\">还没有账号？</text>\r\n\t\t\t\t\t<text class=\"register-action\" @click=\"goToRegister\">立即注册</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"footer\">\r\n\t\t\t<text class=\"footer-text\">登录即表示同意用户协议和隐私政策</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { loginUser, checkUser } from '@/api/user/user.js'\r\n\timport { setToken, setUserInfo } from '@/utils/token.js'\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取微信授权码\r\n\t\t\tgetWechatCode() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\t\t\tresolve(res.code)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\treject(new Error('获取授权码失败'))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\treject(err)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理登录\r\n\t\t\tasync handleLogin() {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取微信授权码\r\n\t\t\t\t\tconst code = await this.getWechatCode()\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 调用登录接口\r\n\t\t\t\t\tconst response = await loginUser({ code })\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\t// 登录成功，保存用户信息\r\n\t\t\t\t\t\tconst userInfo = response.user || response.data\r\n\t\t\t\t\t\tconst token = response.token || 'wechat_token_' + userInfo.userId\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 保存token和用户信息到本地存储\r\n\t\t\t\t\t\tsetToken(token)\r\n\t\t\t\t\t\tsetUserInfo(userInfo)\r\n\t\t\t\t\t\tif (userInfo.openid) {\r\n\t\t\t\t\t\t\tuni.setStorageSync('openid', userInfo.openid)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '登录成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 延迟跳转到首页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 登录失败，可能是用户未注册\r\n\t\t\t\t\t\tif (response.msg.includes('用户不存在')) {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: '您还未注册，是否前往注册？',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tthis.goToRegister()\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: response.msg || '登录失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('登录失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 跳转到注册页面\r\n\t\t\tgoToRegister() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/register/register'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 检查用户登录状态\r\n\t\t\tasync checkLoginStatus() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst code = await this.getWechatCode()\r\n\t\t\t\t\tconst response = await checkUser({ code })\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\t// 用户已注册，直接登录\r\n\t\t\t\t\t\tconst userInfo = response.data\r\n\t\t\t\t\t\tconst token = 'wechat_token_' + userInfo.userId\r\n\t\t\t\t\t\tsetToken(token)\r\n\t\t\t\t\t\tsetUserInfo(userInfo)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\t// 用户未注册或检查失败，显示登录界面\r\n\t\t\t\t\tconsole.log('用户未注册或检查失败')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时检查登录状态\r\n\t\t\t// this.checkLoginStatus()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.login-container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tpadding: 80rpx 40rpx 40rpx;\r\n\t}\r\n\t\r\n\t.header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 120rpx;\r\n\t\t\r\n\t\t.logo {\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 160rpx;\r\n\t\t\tmargin-bottom: 40rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #fff;\r\n\t\t\tdisplay: block;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.subtitle {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.login-content {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\t\r\n\t\t.welcome-text {\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-bottom: 80rpx;\r\n\t\t\t\r\n\t\t\t.welcome-title {\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.welcome-desc {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.button-container {\r\n\t\t\t.login-btn {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 88rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tborder: none;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-bottom: 40rpx;\r\n\t\t\t\t\r\n\t\t\t\t&:disabled {\r\n\t\t\t\t\tbackground-color: rgba(255, 255, 255, 0.7);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.btn-text {\r\n\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\tcolor: #667eea;\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.register-link {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\t\r\n\t\t\t\t.register-text {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t.register-action {\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\ttext-decoration: underline;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.footer {\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 60rpx;\r\n\t\t\r\n\t\t.footer-text {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.6);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753973584480\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}