<template>
  <view class="container">

    <!-- 搜索区域 -->
    <view class="search-section">
      <view class="search-bar">
        <picker @change="onCategoryChange" :value="categoryIndex" :range="categories">
          <view class="category-picker">
            <text>{{ categories[categoryIndex] }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <input 
          class="search-input" 
          type="text" 
          placeholder="输入食物名查询升糖指数GI值"
          v-model="searchKeyword"
        />
        <button class="search-btn" @click="searchFood">查询</button>
      </view>
    </view>

    <!-- 标题区域 -->
    <view class="title-section">
      <text class="main-title">—— 食物升糖指数（GI）查询 ——</text>
    </view>

    <!-- 分类筛选 -->
    <view class="filter-section">
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'all' }"
        @click="setFilter('all')"
      >
        全部食物
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'low' }"
        @click="setFilter('low')"
      >
        低升糖食物
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'medium' }"
        @click="setFilter('medium')"
      >
        中升糖食物
      </view>
      <view 
        class="filter-item" 
        :class="{ active: currentFilter === 'high' }"
        @click="setFilter('high')"
      >
        高升糖食物
      </view>
    </view>

    <!-- 排序选择 -->
    <view class="sort-section">
      <text class="sort-label">排序依据：</text>
      <picker @change="onSortChange" :value="sortIndex" :range="sortOptions">
        <view class="sort-picker">
          <text>{{ sortOptions[sortIndex] }}</text>
          <text class="picker-arrow">▼</text>
        </view>
      </picker>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-section">
      <text>加载中...</text>
    </view>

    <!-- 食物列表 -->
    <view class="food-list" v-else>
      <view 
        class="food-item" 
        v-for="(food, index) in filteredFoods" 
        :key="index"
      >
        <image class="food-image" :src="getFoodImage(food.foodName)" mode="aspectFill"></image>
        <view class="food-info">
          <text class="food-name">{{ food.foodName }}</text>
          <view class="food-details">
            <text class="gi-value">升糖指数(GI)：{{ food.glycemicIndex }} ({{ getGiLevel(food.glycemicIndex) }})</text>
            <text class="gl-value">升糖负荷(GL)：{{ food.glycemicLoad || '暂无' }} ({{ getGlLevel(food.glycemicLoad) }})</text>
            <text class="calorie-value">热量：{{ food.calories || '暂无' }} 千卡/100克</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 无数据提示 -->
    <view v-if="!loading && filteredFoods.length === 0" class="no-data">
      <text>暂无数据</text>
    </view>

  </view>
</template>

<script>
import foodApi from '@/api/food/index.js'

export default {
  data() {
    return {
      searchKeyword: '',
      categoryIndex: 0,
      categories: [], // 改为动态获取
      currentFilter: 'all',
      sortIndex: 0,
      sortOptions: ['默认排序', 'GI值升序', 'GI值降序', '热量升序', '热量降序'],
      loading: false,
      foods: []
    }
  },
  computed: {
    filteredFoods() {
      let result = this.foods;
      
      // 根据分类筛选
      if (this.currentFilter !== 'all') {
        result = result.filter(food => {
          if (this.currentFilter === 'low') return food.glycemicIndex < 55;
          if (this.currentFilter === 'medium') return food.glycemicIndex >= 55 && food.glycemicIndex < 70;
          if (this.currentFilter === 'high') return food.glycemicIndex >= 70;
        });
      }
      
      // 搜索时不再在计算属性中过滤，因为已经从后端获取了过滤后的数据
      // 如果是通过搜索获取的数据，就不需要再次过滤
      // if (this.searchKeyword) {
      //   result = result.filter(food => 
      //     food.foodName.includes(this.searchKeyword)
      //   );
      // }
      
      // 根据类别筛选（只在没有搜索关键词时生效）
      if (this.categoryIndex > 0 && !this.searchKeyword) {
        const selectedCategory = this.categories[this.categoryIndex];
        result = result.filter(food => food.foodCategory === selectedCategory);
      }
      
      // 排序
      if (this.sortIndex === 1) { // GI值升序
        result.sort((a, b) => a.glycemicIndex - b.glycemicIndex);
      } else if (this.sortIndex === 2) { // GI值降序
        result.sort((a, b) => b.glycemicIndex - a.glycemicIndex);
      } else if (this.sortIndex === 3) { // 热量升序
        result.sort((a, b) => (a.calories || 0) - (b.calories || 0));
      } else if (this.sortIndex === 4) { // 热量降序
        result.sort((a, b) => (b.calories || 0) - (a.calories || 0));
      }
      
      return result;
    }
  },
  onLoad() {
    this.loadCategories();
    this.loadFoods();
  },
  methods: {
    // 加载食物数据
    async loadFoods() {
      this.loading = true;
      try {
        const response = await foodApi.getFoodList({
          status: '0' // 只获取正常状态的食物
        });
        
        if (response.code === 200) {
          this.foods = response.data || [];
        } else {
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取食物数据失败:', error);
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 搜索食物
    async searchFood() {
      if (!this.searchKeyword.trim()) {
        this.loadFoods();
        return;
      }

      this.loading = true;
      try {
        const response = await foodApi.searchFood({
          keyword: this.searchKeyword
        });
        
        console.log('搜索返回数据:', response);
        
        if (response.code === 200) {
          this.foods = response.data || [];
          // 搜索时重置分类选择器
          this.categoryIndex = 0;
          console.log('设置foods数据:', this.foods);
          console.log('filteredFoods计算结果:', this.filteredFoods);
        } else {
          uni.showToast({
            title: '搜索失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('搜索失败:', error);
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载食物分类
    async loadCategories() {
      try {
        const response = await foodApi.getCategories();
        if (response.code === 200 && Array.isArray(response.data)) {
          this.categories = ['选择类别', ...response.data.map(item => item.categoryName)];
        } else {
          this.categories = ['选择类别'];
        }
      } catch (error) {
        this.categories = ['选择类别'];
      }
    },

    // 根据类别筛选
    async onCategoryChange(e) {
      this.categoryIndex = e.detail.value;
      // 分类筛选时清空搜索关键词
      this.searchKeyword = '';
      
      if (this.categoryIndex > 0) {
        const selectedCategory = this.categories[this.categoryIndex];
        this.loading = true;
        try {
          const response = await foodApi.getFoodByCategory({
            category: selectedCategory
          });
          
          if (response.code === 200) {
            this.foods = response.data || [];
          }
        } catch (error) {
          console.error('获取类别数据失败:', error);
        } finally {
          this.loading = false;
        }
      } else {
        this.loadFoods();
      }
    },

    onSortChange(e) {
      this.sortIndex = e.detail.value;
    },

    setFilter(filter) {
      this.currentFilter = filter;
    },

    // 获取升糖指数等级
    getGiLevel(gi) {
      if (!gi) return '暂无';
      if (gi < 55) return '低';
      if (gi < 70) return '中';
      return '高';
    },

    // 获取升糖负荷等级
    getGlLevel(gl) {
      if (!gl) return '暂无';
      if (gl < 10) return '低';
      if (gl < 20) return '中';
      return '高';
    },

    // 获取食物图片
    getFoodImage(foodName) {
      // 这里可以根据食物名称返回对应的图片路径
      // 如果没有图片，返回默认图片
      const imageMap = {
        '苹果': '/static/foods/apple.jpg',
        '香蕉': '/static/foods/banana.jpg',
        '西瓜': '/static/foods/watermelon.jpg',
        '白米饭': '/static/foods/rice.jpg',
        '燕麦': '/static/foods/oats.jpg',
        '全麦面包': '/static/foods/wheat-bread.jpg',
        '红薯': '/static/foods/sweet-potato.jpg',
        '胡萝卜': '/static/foods/carrot.jpg',
        '西兰花': '/static/foods/broccoli.jpg',
        '鸡胸肉': '/static/foods/chicken-breast.jpg',
        '核桃': '/static/foods/walnut.jpg',
        '杏仁': '/static/foods/almond.jpg'
      };
      
      return imageMap[foodName] || '/static/foods/default.jpg';
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部绿色背景区域 */
.header-section {
  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
  padding: 20rpx 30rpx 60rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);
  border-radius: 0 0 32rpx 32rpx;
  z-index: 2;
}

.header-section::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  right: -50rpx;
  width: 200rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.wechat-group-banner {
  text-align: center;
  color: white;
}

.banner-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 搜索区域 */
.search-section {
  padding: 30rpx;
  background: white;
  margin-top: -30rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.10), 0 1rpx 2rpx rgba(0,0,0,0.04);
  z-index: 3;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.category-picker {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  min-width: 160rpx;
  font-size: 28rpx;
}

.picker-arrow {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.search-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);
  transition: box-shadow 0.2s, transform 0.2s;
}
.search-btn:active {
  box-shadow: 0 2rpx 4rpx rgba(76, 175, 80, 0.10);
  transform: translateY(2rpx) scale(0.98);
}

/* 标题区域 */
.title-section {
  padding: 40rpx 30rpx;
  text-align: center;
  background: white;
}

.main-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4CAF50;
}

/* 分类筛选 */
.filter-section {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
  padding: 40rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.filter-item {
  flex: 1;
  padding: 20rpx 10rpx;
  background: #f8f8f8;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s;
  border: 2rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  position: relative;
}

.filter-item.active {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
  box-shadow: 0 6rpx 18rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);
}

/* 排序选择 */
.sort-section {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.sort-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.sort-picker {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
}

.loading-section text {
  font-size: 28rpx;
  color: #666;
}

/* 无数据提示 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 15rpx;
}

.no-data text {
  font-size: 28rpx;
  color: #999;
}

/* 食物列表 */
.food-list {
  padding: 0 30rpx;
}

.food-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.13), 0 2rpx 8rpx rgba(0,0,0,0.06);
  transition: box-shadow 0.2s, transform 0.2s;
  position: relative;
  z-index: 1;
}
.food-item:active {
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.10);
  transform: translateY(2rpx) scale(0.98);
}

.food-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  margin-right: 30rpx;
  background: #f0f0f0;
}

.food-info {
  flex: 1;
}

.food-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.food-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.gi-value {
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: 500;
}

.gl-value {
  font-size: 26rpx;
  color: #FF9800;
  font-weight: 500;
}

.calorie-value {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 食物列表底部间距 */
.food-list {
  padding-bottom: 40rpx;
}
</style>