@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.home-container.data-v-92bb8f34 {
  min-height: 100vh;
  background-color: #f5f7fa;
}
.header-section.data-v-92bb8f34 {
  position: relative;
  height: 300rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  overflow: hidden;
}
.header-section .welcome-text.data-v-92bb8f34 {
  position: relative;
  z-index: 2;
}
.header-section .welcome-text .greeting.data-v-92bb8f34 {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  display: block;
  margin-bottom: 10rpx;
}
.header-section .welcome-text .subtitle.data-v-92bb8f34 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.header-section .header-bg.data-v-92bb8f34 {
  position: absolute;
  right: -50rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 200rpx;
  height: 200rpx;
  opacity: 0.3;
}
.modules-section.data-v-92bb8f34 {
  padding: 40rpx;
  margin-top: -60rpx;
  position: relative;
  z-index: 3;
}
.modules-section .modules-grid.data-v-92bb8f34 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}
.modules-section .modules-grid .module-item.data-v-92bb8f34 {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.modules-section .modules-grid .module-item.data-v-92bb8f34:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.modules-section .modules-grid .module-item .module-icon.data-v-92bb8f34 {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
  background-color: #f0f2f5;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modules-section .modules-grid .module-item .module-icon image.data-v-92bb8f34 {
  width: 50rpx;
  height: 50rpx;
}
.modules-section .modules-grid .module-item .module-title.data-v-92bb8f34 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.modules-section .modules-grid .module-item .module-desc.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #999;
}
.quick-actions.data-v-92bb8f34 {
  margin: 40rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
}
.quick-actions .section-title.data-v-92bb8f34 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.quick-actions .action-list .action-item.data-v-92bb8f34 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.quick-actions .action-list .action-item.data-v-92bb8f34:last-child {
  border-bottom: none;
}
.quick-actions .action-list .action-item .action-text.data-v-92bb8f34 {
  font-size: 28rpx;
  color: #333;
}
.quick-actions .action-list .action-item .action-arrow.data-v-92bb8f34 {
  font-size: 24rpx;
  color: #ccc;
}
