(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-communication-communication"],{"0c27":function(e,t,n){var i=n("7e52");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("eadc9daa",i,!0,{sourceMap:!1,shadowMode:!1})},"1ad5":function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("3d6e")),o={getUserListByKeyword:function(e){return(0,a.default)({url:"/app/user/getUserListByKeyword",method:"get",params:e})},getUserInfo:function(e){return(0,a.default)({url:"/app/user/getUserInfo",method:"get",params:e})}};t.default=o},"27e2":function(e,t,n){var i=n("8ca1");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var a=n("4f06").default;a("74983a2e",i,!0,{sourceMap:!1,shadowMode:!1})},3900:function(e,t,n){"use strict";n.r(t);var i=n("74cd"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},"3c90":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uSearch:n("f473").default,uList:n("f1af").default,uListItem:n("bd2a").default,uCell:n("d8f1").default,uAvatar:n("4e72").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"all-view"},[e.keyword?n("v-uni-view",{staticClass:"top-search"},[n("u-search",{attrs:{shape:"square",placeholder:"请输入手机号或姓名",inputAlign:"center",bgColor:"#ffffff",actionText:"取消",animation:!1},on:{search:function(t){arguments[0]=t=e.$handleEvent(t),e.searchUsers.apply(void 0,arguments)},custom:function(t){arguments[0]=t=e.$handleEvent(t),e.cancelSearch.apply(void 0,arguments)}},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1):n("v-uni-view",{staticClass:"top-search"},[n("u-search",{attrs:{shape:"square",showAction:!1,placeholder:"请输入手机号或姓名",inputAlign:"center",bgColor:"#ffffff"},on:{search:function(t){arguments[0]=t=e.$handleEvent(t),e.searchUsers.apply(void 0,arguments)}},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1),n("v-uni-view",{staticClass:"view-title",staticStyle:{"overflow-x":"auto","white-space":"nowrap",display:"flex"}},[n("v-uni-view",{on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickTopDept(e.index)}}},[e._v("企业通讯录")]),e._l(e.topViewList,(function(t,i){return n("v-uni-view",{key:i,staticStyle:{color:"#4871c0"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickTopDeptList(i)}}},[e._v("> "+e._s(t.deptName))])})),n("v-uni-view",{staticStyle:{width:"50rpx"}})],2),n("v-uni-view",{staticClass:"organ-list-view"},[n("u-list",[e._l(e.userList,(function(t,i){return n("u-list-item",{key:i},[n("u-cell",{attrs:{title:t.userName,isLink:!0,"arrow-direction":"right",size:"large"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.getUserInfo(t)}}},[n("v-uni-view",{staticStyle:{"font-size":"20rpx","margin-top":"2rpx",color:"#909193"},attrs:{slot:"label"},slot:"label"},[e._v(e._s(t.deptChains))]),n("u-avatar",{attrs:{slot:"icon",shape:"square",size:"35",src:"../../static/communication/user.png",customStyle:"margin: -3px 5px -3px 0;"},slot:"icon"})],1)],1)})),e._l(e.deptList,(function(t,i){return n("u-list-item",{key:i},[n("u-cell",{attrs:{title:t.deptName+"("+t.employeeCount+")",isLink:!0,"arrow-direction":"right",size:"large"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.getChildenDepts(t)}}},[n("u-avatar",{attrs:{slot:"icon",shape:"square",size:"35",src:"../../static/communication/dept.png",customStyle:"margin: -3px 5px -3px 0;"},slot:"icon"})],1)],1)})),n("v-uni-view",{staticStyle:{height:"200rpx"}})],2)],1)],1)},o=[]},"48d6":function(e,t,n){"use strict";var i=n("0c27"),a=n.n(i);a.a},"5b8f":function(e,t,n){"use strict";var i=n("27e2"),a=n.n(i);a.a},"74cd":function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("838c")),o={name:"u-search",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(e){this.$emit("input",e),this.$emit("change",e)},value:{immediate:!0,handler:function(e){this.keyword=e}}},computed:{showActionBtn:function(){return!this.animation&&this.showAction}},methods:{inputChange:function(e){this.keyword=e.detail.value},clear:function(){var e=this;this.keyword="",this.$nextTick((function(){e.$emit("clear")}))},search:function(e){this.$emit("search",e.detail.value);try{uni.hideKeyboard()}catch(e){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(e){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var e=this;setTimeout((function(){e.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")},clickIcon:function(){this.$emit("clickIcon")}}};t.default=o},"7e52":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-6e039928]{height:100%;width:100%;background-color:#f3f4f6}body.?%PAGE?%[data-v-6e039928]{background-color:#f3f4f6}.top-search[data-v-6e039928]{padding:%?20?%}.view-title[data-v-6e039928]{padding-left:%?20?%;padding-top:%?20?%;padding-bottom:%?10?%;display:flex;flex-direction:row;font-size:%?30?%;background-color:#fff}.organ-list-view[data-v-6e039928]{background-color:#fff}.cell-hover-class[data-v-6e039928]{background-color:#4871c0}',""]),e.exports=t},"838c":function(e,t,n){"use strict";n("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("ac1f"),n("841c"),n("a9e3");var i={props:{shape:{type:String,default:uni.$u.props.search.shape},bgColor:{type:String,default:uni.$u.props.search.bgColor},placeholder:{type:String,default:uni.$u.props.search.placeholder},clearabled:{type:Boolean,default:uni.$u.props.search.clearabled},focus:{type:Boolean,default:uni.$u.props.search.focus},showAction:{type:Boolean,default:uni.$u.props.search.showAction},actionStyle:{type:Object,default:uni.$u.props.search.actionStyle},actionText:{type:String,default:uni.$u.props.search.actionText},inputAlign:{type:String,default:uni.$u.props.search.inputAlign},inputStyle:{type:Object,default:uni.$u.props.search.inputStyle},disabled:{type:Boolean,default:uni.$u.props.search.disabled},borderColor:{type:String,default:uni.$u.props.search.borderColor},searchIconColor:{type:String,default:uni.$u.props.search.searchIconColor},color:{type:String,default:uni.$u.props.search.color},placeholderColor:{type:String,default:uni.$u.props.search.placeholderColor},searchIcon:{type:String,default:uni.$u.props.search.searchIcon},searchIconSize:{type:[Number,String],default:uni.$u.props.search.searchIconSize},margin:{type:String,default:uni.$u.props.search.margin},animation:{type:Boolean,default:uni.$u.props.search.animation},value:{type:String,default:uni.$u.props.search.value},maxlength:{type:[String,Number],default:uni.$u.props.search.maxlength},height:{type:[String,Number],default:uni.$u.props.search.height},label:{type:[String,Number,null],default:uni.$u.props.search.label}}};t.default=i},"8ca1":function(e,t,n){var i=n("24fb");t=i(!1),t.push([e.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-313cf2d0], uni-scroll-view[data-v-313cf2d0], uni-swiper-item[data-v-313cf2d0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}[type="search"][data-v-313cf2d0]::-webkit-search-decoration{display:none}.u-search[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center;flex:1}.u-search__content[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center;padding:0 10px;flex:1;justify-content:space-between;border-width:1px;border-color:transparent;border-style:solid;overflow:hidden}.u-search__content__icon[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center}.u-search__content__label[data-v-313cf2d0]{color:#303133;font-size:14px;margin:0 4px}.u-search__content__close[data-v-313cf2d0]{width:20px;height:20px;border-top-left-radius:100px;border-top-right-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;background-color:#c6c7cb;display:flex;flex-direction:row;align-items:center;justify-content:center;-webkit-transform:scale(.82);transform:scale(.82)}.u-search__content__input[data-v-313cf2d0]{flex:1;font-size:14px;line-height:1;margin:0 5px;color:#303133}.u-search__content__input--placeholder[data-v-313cf2d0]{color:#909193}.u-search__action[data-v-313cf2d0]{font-size:14px;color:#303133;width:0;overflow:hidden;transition-property:width;transition-duration:.3s;white-space:nowrap;text-align:center}.u-search__action--active[data-v-313cf2d0]{width:40px;margin-left:5px}',""]),e.exports=t},a5bc:function(e,t,n){"use strict";n.r(t);var i=n("ba9f"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);t["default"]=a.a},ba9f:function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("14d9"),n("fb6a");var a=i(n("e67e")),o=i(n("1ad5")),r={data:function(){return{keyword:"",deptList:[],userList:[],topViewList:[],ifBorder:!1}},onLoad:function(e){this.getTopDeptList()},onShow:function(e){},methods:{getUserInfo:function(e){var t=e.userId;uni.navigateTo({url:"/pages/communication/userInfo?userId="+t})},searchUsers:function(){var e=this,t=this.keyword;o.default.getUserListByKeyword({keyword:t}).then((function(t){uni.hideLoading(),200==t.code&&(e.deptList=[],e.topViewList=[],e.userList=t.data)}))},cancelSearch:function(){this.keyword=null,this.userList=[],this.topViewList=[],this.getTopDeptList()},getTopDeptList:function(){var e=this;a.default.getTopDept().then((function(t){200==t.code&&(e.deptList=t.data)}))},getChildenDepts:function(e){var t=this,n=e.deptId;a.default.getChildenDept({parentId:n}).then((function(n){200==n.code&&(t.topViewList.push(e),t.deptList=n.data.depts,t.userList=n.data.users)}))},clickTopDept:function(){this.getTopDeptList(),this.topViewList=[]},clickTopDeptList:function(e){var t=this,n=this.topViewList[e];this.topViewList=this.topViewList.slice(0,e+1);var i=n.deptId;a.default.getChildenDept({parentId:i}).then((function(e){200==e.code&&(t.deptList=e.data.depts,t.userList=e.data.users)}))}}};t.default=r},e0a6:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return i}));var i={uIcon:n("3ccb").default},a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"u-search",style:[{margin:e.margin},e.$u.addStyle(e.customStyle)],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-search__content",style:{backgroundColor:e.bgColor,borderRadius:"round"==e.shape?"100px":"4px",borderColor:e.borderColor}},[e.$slots.label||null!==e.label?[e._t("label",[n("v-uni-text",{staticClass:"u-search__content__label"},[e._v(e._s(e.label))])])]:e._e(),n("v-uni-view",{staticClass:"u-search__content__icon"},[n("u-icon",{attrs:{size:e.searchIconSize,name:e.searchIcon,color:e.searchIconColor?e.searchIconColor:e.color},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clickIcon.apply(void 0,arguments)}}})],1),n("v-uni-input",{staticClass:"u-search__content__input",style:[{textAlign:e.inputAlign,color:e.color,backgroundColor:e.bgColor,height:e.$u.addUnit(e.height)},e.inputStyle],attrs:{"confirm-type":"search",value:e.value,disabled:e.disabled,focus:e.focus,maxlength:e.maxlength,"placeholder-class":"u-search__content__input--placeholder",placeholder:e.placeholder,"placeholder-style":"color: "+e.placeholderColor,type:"text"},on:{blur:function(t){arguments[0]=t=e.$handleEvent(t),e.blur.apply(void 0,arguments)},confirm:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)},input:function(t){arguments[0]=t=e.$handleEvent(t),e.inputChange.apply(void 0,arguments)},focus:function(t){arguments[0]=t=e.$handleEvent(t),e.getFocus.apply(void 0,arguments)}}}),e.keyword&&e.clearabled&&e.focused?n("v-uni-view",{staticClass:"u-search__content__icon u-search__content__close",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.clear.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}})],1):e._e()],2),n("v-uni-text",{staticClass:"u-search__action",class:[(e.showActionBtn||e.show)&&"u-search__action--active"],style:[e.actionStyle],on:{click:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),e.custom.apply(void 0,arguments)}}},[e._v(e._s(e.actionText))])],1)},o=[]},e67e:function(e,t,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(n("3d6e")),o={getTopDept:function(){return(0,a.default)({url:"/app/dept/getTopDept",method:"get"})},getChildenDept:function(e){return(0,a.default)({url:"/app/dept/getChildenDept",method:"post",data:e})}};t.default=o},f473:function(e,t,n){"use strict";n.r(t);var i=n("e0a6"),a=n("3900");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("5b8f");var r=n("f0c5"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"313cf2d0",null,!1,i["a"],void 0);t["default"]=c.exports},ffed:function(e,t,n){"use strict";n.r(t);var i=n("3c90"),a=n("a5bc");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("48d6");var r=n("f0c5"),c=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"6e039928",null,!1,i["a"],void 0);t["default"]=c.exports}}]);