import request from '@/utils/request.js'

// 用户注册
export function registerUser(data) {
  return request({
    url: '/wechat/user/register',
    method: 'post',
    data: data
  })
}

// 用户登录
export function loginUser(data) {
  return request({
    url: '/wechat/user/login',
    method: 'post',
    data: data
  })
}

// 检查用户是否已注册
export function checkUser(data) {
  return request({
    url: '/wechat/user/checkUser',
    method: 'post',
    data: data
  })
} 