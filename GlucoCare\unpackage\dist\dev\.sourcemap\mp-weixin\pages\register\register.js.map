{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?4ed5", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?6af7", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?737f", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?001c", "uni-app:///pages/register/register.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?bb35", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/register/register.vue?52ff"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "form", "userName", "gender", "age", "height", "weight", "phone", "diabetes<PERSON><PERSON>s", "diabetesOptions", "diabetesText", "methods", "selectGender", "onDiabetesChange", "validateForm", "uni", "title", "icon", "getWechatCode", "provider", "success", "resolve", "reject", "fail", "handleRegister", "code", "registerData", "response", "setTimeout", "url", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,0pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmE7qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACAH;UACAI;UACAC;YACA;cACAC;YACA;cACAC;YACA;UACA;UACAC;YACAD;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAC;gBAEA;gBACAC;kBACAD;kBACAvB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBAAA;gBAAA,OACA;cAAA;gBAAAmB;gBAEA;kBACAZ;oBACAC;oBACAC;kBACA;;kBAEA;kBACAW;oBACAb;sBACAc;oBACA;kBACA;gBACA;kBACAd;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAa;gBACAf;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5NA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,isCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=891c2434&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"register-container\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"title\">用户注册</text>\r\n\t\t\t<text class=\"subtitle\">请完善您的个人信息</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"form-container\">\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">姓名 <text class=\"required\">*</text></text>\r\n\t\t\t\t<input class=\"input\" v-model=\"form.userName\" placeholder=\"请输入您的姓名\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">性别 <text class=\"required\">*</text></text>\r\n\t\t\t\t<view class=\"radio-group\">\r\n\t\t\t\t\t<label class=\"radio-item\">\r\n\t\t\t\t\t\t<radio value=\"0\" :checked=\"form.gender === '0'\" @click=\"selectGender('0')\" />\r\n\t\t\t\t\t\t<text>男</text>\r\n\t\t\t\t\t</label>\r\n\t\t\t\t\t<label class=\"radio-item\">\r\n\t\t\t\t\t\t<radio value=\"1\" :checked=\"form.gender === '1'\" @click=\"selectGender('1')\" />\r\n\t\t\t\t\t\t<text>女</text>\r\n\t\t\t\t\t</label>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">年龄 <text class=\"required\">*</text></text>\r\n\t\t\t\t<input class=\"input\" v-model=\"form.age\" type=\"number\" placeholder=\"请输入您的年龄\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">身高(cm) <text class=\"required\">*</text></text>\r\n\t\t\t\t<input class=\"input\" v-model=\"form.height\" type=\"digit\" placeholder=\"请输入您的身高\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">体重(kg) <text class=\"required\">*</text></text>\r\n\t\t\t\t<input class=\"input\" v-model=\"form.weight\" type=\"digit\" placeholder=\"请输入您的体重\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">联系方式</text>\r\n\t\t\t\t<input class=\"input\" v-model=\"form.phone\" placeholder=\"请输入您的联系方式\" />\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"form-item\">\r\n\t\t\t\t<text class=\"label\">患糖尿病时长</text>\r\n\t\t\t\t<picker mode=\"selector\" :range=\"diabetesOptions\" @change=\"onDiabetesChange\">\r\n\t\t\t\t\t<view class=\"picker-input\">\r\n\t\t\t\t\t\t<text class=\"picker-text\">{{diabetesText}}</text>\r\n\t\t\t\t\t\t<text class=\"picker-arrow\">></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</picker>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<view class=\"button-container\">\r\n\t\t\t<button class=\"register-btn\" @click=\"handleRegister\" :disabled=\"loading\">\r\n\t\t\t\t{{loading ? '注册中...' : '注册'}}\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { registerUser } from '@/api/user/user.js'\r\n\t\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\tform: {\r\n\t\t\t\t\tuserName: '',\r\n\t\t\t\t\tgender: '',\r\n\t\t\t\t\tage: '',\r\n\t\t\t\t\theight: '',\r\n\t\t\t\t\tweight: '',\r\n\t\t\t\t\tphone: '',\r\n\t\t\t\t\tdiabetesYears: 0\r\n\t\t\t\t},\r\n\t\t\t\tdiabetesOptions: ['无', '1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年', '10年以上'],\r\n\t\t\t\tdiabetesText: '无'\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 选择性别\r\n\t\t\tselectGender(gender) {\r\n\t\t\t\tthis.form.gender = gender\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 选择患病时长\r\n\t\t\tonDiabetesChange(e) {\r\n\t\t\t\tconst index = e.detail.value\r\n\t\t\t\tthis.diabetesText = this.diabetesOptions[index]\r\n\t\t\t\tthis.form.diabetesYears = index\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 表单验证\r\n\t\t\tvalidateForm() {\r\n\t\t\t\tif (!this.form.userName.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入姓名',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.form.gender) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择性别',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.form.age || this.form.age <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入有效年龄',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.form.height || this.form.height <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入有效身高',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (!this.form.weight || this.form.weight <= 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入有效体重',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\treturn true\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 获取微信授权码\r\n\t\t\tgetWechatCode() {\r\n\t\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.code) {\r\n\t\t\t\t\t\t\t\tresolve(res.code)\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\treject(new Error('获取授权码失败'))\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\treject(err)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 处理注册\r\n\t\t\tasync handleRegister() {\r\n\t\t\t\tif (!this.validateForm()) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\t\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取微信授权码\r\n\t\t\t\t\tconst code = await this.getWechatCode()\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 构造注册数据\r\n\t\t\t\t\tconst registerData = {\r\n\t\t\t\t\t\tcode: code,\r\n\t\t\t\t\t\tuserName: this.form.userName,\r\n\t\t\t\t\t\tgender: this.form.gender,\r\n\t\t\t\t\t\tage: parseInt(this.form.age),\r\n\t\t\t\t\t\theight: parseFloat(this.form.height),\r\n\t\t\t\t\t\tweight: parseFloat(this.form.weight),\r\n\t\t\t\t\t\tphone: this.form.phone,\r\n\t\t\t\t\t\tdiabetesYears: this.form.diabetesYears\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 调用注册接口\r\n\t\t\t\t\tconst response = await registerUser(registerData)\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (response.code === 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '注册成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 延迟跳转到首页\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 1500)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: response.msg || '注册失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('注册失败:', error)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '注册失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.register-container {\r\n\t\tpadding: 40rpx;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t.header {\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 60rpx;\r\n\t\t\r\n\t\t.title {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t\tdisplay: block;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\t\t\r\n\t\t.subtitle {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #666;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.form-container {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tmargin-bottom: 60rpx;\r\n\t}\r\n\t\r\n\t.form-item {\r\n\t\tmargin-bottom: 40rpx;\r\n\t\t\r\n\t\t.label {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #333;\r\n\t\t\tdisplay: block;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\r\n\t\t\t.required {\r\n\t\t\t\tcolor: #ff4757;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.input {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder: 2rpx solid #e1e1e1;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t\r\n\t\t\t&:focus {\r\n\t\t\t\tborder-color: #007aff;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.radio-group {\r\n\t\t\tdisplay: flex;\r\n\t\t\tgap: 40rpx;\r\n\t\t\t\r\n\t\t\t.radio-item {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tgap: 10rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t.picker-input {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\theight: 80rpx;\r\n\t\t\tborder: 2rpx solid #e1e1e1;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\t\r\n\t\t\t.picker-text {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.picker-arrow {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t.button-container {\r\n\t\t.register-btn {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 88rpx;\r\n\t\t\tbackground-color: #007aff;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tborder-radius: 44rpx;\r\n\t\t\tborder: none;\r\n\t\t\t\r\n\t\t\t&:disabled {\r\n\t\t\t\tbackground-color: #ccc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&id=891c2434&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&id=891c2434&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753972579768\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}