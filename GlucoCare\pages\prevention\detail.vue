<template>
  <view class="container">
    <view class="title">{{ detail.title }}</view>
    <video v-if="detail.type === '2' && detail.videoUrl" :src="detail.videoUrl" controls style="width:100%;margin:20rpx 0;"/>
    <rich-text class="content" :nodes="detail.content"></rich-text>
  </view>
</template>
<script>
import { getPreventionDetail } from './index.js'
import APP_CONFIG from '../../config/config.js'
export default {
  data() {
    return { detail: {} }
  },
 onLoad(options) {
  getPreventionDetail(options.id).then(res => {
    this.detail = res.data || {}
    if (this.detail.content) {
      this.detail.content = this.detail.content.replace(
        /<img([^>]*)src=['"]([^'"]+)['"]([^>]*)>/gi,
        (match, p1, src, p2) => {
          // 1. 补全图片src为绝对路径
          let newSrc = src;
          if (!/^https?:\/\//.test(src)) {
            newSrc = APP_CONFIG.VUE_APP_API_HOST_DEFAULT + (src.startsWith('/') ? src : '/' + src);
          }
          // 2. 强制添加内联样式，兼容小程序
          // 先移除原有style
          let tag = `<img${p1}src="${newSrc}"${p2}>`;
          tag = tag.replace(/style=['\"][^'\"]*['\"]/, '');
          // 再加上新style（注意margin-left/right:auto和display:block）
          return tag.replace(
            /<img/,
            `<img style="display:block;margin:20rpx auto 20rpx auto;max-width:100%;height:auto;clear:both;margin-left:auto;margin-right:auto;"`
          );
        }
      );
    }
  })
},
}
</script>
<style scoped>
.container { padding: 24rpx; }
.title { font-size: 36rpx; font-weight: bold; margin-bottom: 20rpx; text-align: center; }
.cover { width: 100%; border-radius: 8rpx; margin-bottom: 20rpx; }
.content { font-size: 30rpx; color: #222; }
/* 让富文本图片单独成行并居中，图片后文字自动换行 */
:deep(.content) img,
>>> .content img,
    /deep/ .content img {
  display: block !important;
  margin: 20rpx auto !important;
  max-width: 100% !important;
  height: auto !important;
  clear: both;
}
</style>