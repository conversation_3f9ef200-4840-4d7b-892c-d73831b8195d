{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?c1e9", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?cc37", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?cb77", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?28d5", "uni-app:///pages/index/index.vue", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?bb33", "webpack:///D:/Codes/whpe_Gitee/XX/GlucoCare/pages/index/index.vue?a983"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "categoryIndex", "categories", "currentFilter", "sortIndex", "sortOptions", "loading", "foods", "computed", "filteredFoods", "result", "onLoad", "methods", "loadFoods", "foodApi", "status", "response", "uni", "title", "icon", "console", "searchFood", "keyword", "loadCategories", "onCategoryChange", "selectedCate<PERSON><PERSON>", "category", "onSortChange", "setFilter", "getGiLevel", "getGlLevel", "getFoodImage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACgL;AAChL,gBAAgB,qLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAspB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuG1qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;;MAEA;MACA;QACAC;UACA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;QACA;QACAA;UAAA;QAAA;MACA;;MAEA;MACA;QAAA;QACAA;UAAA;QAAA;MACA;QAAA;QACAA;UAAA;QAAA;MACA;QAAA;QACAA;UAAA;QAAA;MACA;QAAA;QACAA;UAAA;QAAA;MACA;MAEA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;gBACA;cAAA;gBAFAC;gBAIA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAEAP;kBACAQ;gBACA;cAAA;gBAFAN;gBAIAI;gBAEA;kBACA;kBACA;kBACA;kBACAA;kBACAA;gBACA;kBACAH;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAT;cAAA;gBAAAE;gBACA;kBACA;oBAAA;kBAAA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAEAX;kBACAY;gBACA;cAAA;gBAFAV;gBAIA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAI;cAAA;gBAAA;gBAEA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAO;MACA;IACA;IAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAs9B,CAAgB,o7BAAG,EAAC,C;;;;;;;;;;;ACA1+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.loading\n    ? _vm.__map(_vm.filteredFoods, function (food, index) {\n        var $orig = _vm.__get_orig(food)\n        var m0 = _vm.getFoodImage(food.foodName)\n        var m1 = _vm.getGiLevel(food.glycemicIndex)\n        var m2 = _vm.getGlLevel(food.glycemicLoad)\n        return {\n          $orig: $orig,\n          m0: m0,\n          m1: m1,\n          m2: m2,\n        }\n      })\n    : null\n  var g0 = !_vm.loading && _vm.filteredFoods.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n\n    <!-- 搜索区域 -->\n    <view class=\"search-section\">\n      <view class=\"search-bar\">\n        <picker @change=\"onCategoryChange\" :value=\"categoryIndex\" :range=\"categories\">\n          <view class=\"category-picker\">\n            <text>{{ categories[categoryIndex] }}</text>\n            <text class=\"picker-arrow\">▼</text>\n          </view>\n        </picker>\n        <input \n          class=\"search-input\" \n          type=\"text\" \n          placeholder=\"输入食物名查询升糖指数GI值\"\n          v-model=\"searchKeyword\"\n        />\n        <button class=\"search-btn\" @click=\"searchFood\">查询</button>\n      </view>\n    </view>\n\n    <!-- 标题区域 -->\n    <view class=\"title-section\">\n      <text class=\"main-title\">—— 食物升糖指数（GI）查询 ——</text>\n    </view>\n\n    <!-- 分类筛选 -->\n    <view class=\"filter-section\">\n      <view \n        class=\"filter-item\" \n        :class=\"{ active: currentFilter === 'all' }\"\n        @click=\"setFilter('all')\"\n      >\n        全部食物\n      </view>\n      <view \n        class=\"filter-item\" \n        :class=\"{ active: currentFilter === 'low' }\"\n        @click=\"setFilter('low')\"\n      >\n        低升糖食物\n      </view>\n      <view \n        class=\"filter-item\" \n        :class=\"{ active: currentFilter === 'medium' }\"\n        @click=\"setFilter('medium')\"\n      >\n        中升糖食物\n      </view>\n      <view \n        class=\"filter-item\" \n        :class=\"{ active: currentFilter === 'high' }\"\n        @click=\"setFilter('high')\"\n      >\n        高升糖食物\n      </view>\n    </view>\n\n    <!-- 排序选择 -->\n    <view class=\"sort-section\">\n      <text class=\"sort-label\">排序依据：</text>\n      <picker @change=\"onSortChange\" :value=\"sortIndex\" :range=\"sortOptions\">\n        <view class=\"sort-picker\">\n          <text>{{ sortOptions[sortIndex] }}</text>\n          <text class=\"picker-arrow\">▼</text>\n        </view>\n      </picker>\n    </view>\n\n    <!-- 加载状态 -->\n    <view v-if=\"loading\" class=\"loading-section\">\n      <text>加载中...</text>\n    </view>\n\n    <!-- 食物列表 -->\n    <view class=\"food-list\" v-else>\n      <view \n        class=\"food-item\" \n        v-for=\"(food, index) in filteredFoods\" \n        :key=\"index\"\n      >\n        <image class=\"food-image\" :src=\"getFoodImage(food.foodName)\" mode=\"aspectFill\"></image>\n        <view class=\"food-info\">\n          <text class=\"food-name\">{{ food.foodName }}</text>\n          <view class=\"food-details\">\n            <text class=\"gi-value\">升糖指数(GI)：{{ food.glycemicIndex }} ({{ getGiLevel(food.glycemicIndex) }})</text>\n            <text class=\"gl-value\">升糖负荷(GL)：{{ food.glycemicLoad || '暂无' }} ({{ getGlLevel(food.glycemicLoad) }})</text>\n            <text class=\"calorie-value\">热量：{{ food.calories || '暂无' }} 千卡/100克</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 无数据提示 -->\n    <view v-if=\"!loading && filteredFoods.length === 0\" class=\"no-data\">\n      <text>暂无数据</text>\n    </view>\n\n  </view>\n</template>\n\n<script>\nimport foodApi from '@/api/food/index.js'\n\nexport default {\n  data() {\n    return {\n      searchKeyword: '',\n      categoryIndex: 0,\n      categories: [], // 改为动态获取\n      currentFilter: 'all',\n      sortIndex: 0,\n      sortOptions: ['默认排序', 'GI值升序', 'GI值降序', '热量升序', '热量降序'],\n      loading: false,\n      foods: []\n    }\n  },\n  computed: {\n    filteredFoods() {\n      let result = this.foods;\n      \n      // 根据分类筛选\n      if (this.currentFilter !== 'all') {\n        result = result.filter(food => {\n          if (this.currentFilter === 'low') return food.glycemicIndex < 55;\n          if (this.currentFilter === 'medium') return food.glycemicIndex >= 55 && food.glycemicIndex < 70;\n          if (this.currentFilter === 'high') return food.glycemicIndex >= 70;\n        });\n      }\n      \n      // 搜索时不再在计算属性中过滤，因为已经从后端获取了过滤后的数据\n      // 如果是通过搜索获取的数据，就不需要再次过滤\n      // if (this.searchKeyword) {\n      //   result = result.filter(food => \n      //     food.foodName.includes(this.searchKeyword)\n      //   );\n      // }\n      \n      // 根据类别筛选（只在没有搜索关键词时生效）\n      if (this.categoryIndex > 0 && !this.searchKeyword) {\n        const selectedCategory = this.categories[this.categoryIndex];\n        result = result.filter(food => food.foodCategory === selectedCategory);\n      }\n      \n      // 排序\n      if (this.sortIndex === 1) { // GI值升序\n        result.sort((a, b) => a.glycemicIndex - b.glycemicIndex);\n      } else if (this.sortIndex === 2) { // GI值降序\n        result.sort((a, b) => b.glycemicIndex - a.glycemicIndex);\n      } else if (this.sortIndex === 3) { // 热量升序\n        result.sort((a, b) => (a.calories || 0) - (b.calories || 0));\n      } else if (this.sortIndex === 4) { // 热量降序\n        result.sort((a, b) => (b.calories || 0) - (a.calories || 0));\n      }\n      \n      return result;\n    }\n  },\n  onLoad() {\n    this.loadCategories();\n    this.loadFoods();\n  },\n  methods: {\n    // 加载食物数据\n    async loadFoods() {\n      this.loading = true;\n      try {\n        const response = await foodApi.getFoodList({\n          status: '0' // 只获取正常状态的食物\n        });\n        \n        if (response.code === 200) {\n          this.foods = response.data || [];\n        } else {\n          uni.showToast({\n            title: '获取数据失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('获取食物数据失败:', error);\n        uni.showToast({\n          title: '网络错误',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 搜索食物\n    async searchFood() {\n      if (!this.searchKeyword.trim()) {\n        this.loadFoods();\n        return;\n      }\n\n      this.loading = true;\n      try {\n        const response = await foodApi.searchFood({\n          keyword: this.searchKeyword\n        });\n        \n        console.log('搜索返回数据:', response);\n        \n        if (response.code === 200) {\n          this.foods = response.data || [];\n          // 搜索时重置分类选择器\n          this.categoryIndex = 0;\n          console.log('设置foods数据:', this.foods);\n          console.log('filteredFoods计算结果:', this.filteredFoods);\n        } else {\n          uni.showToast({\n            title: '搜索失败',\n            icon: 'none'\n          });\n        }\n      } catch (error) {\n        console.error('搜索失败:', error);\n        uni.showToast({\n          title: '网络错误',\n          icon: 'none'\n        });\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载食物分类\n    async loadCategories() {\n      try {\n        const response = await foodApi.getCategories();\n        if (response.code === 200 && Array.isArray(response.data)) {\n          this.categories = ['选择类别', ...response.data.map(item => item.categoryName)];\n        } else {\n          this.categories = ['选择类别'];\n        }\n      } catch (error) {\n        this.categories = ['选择类别'];\n      }\n    },\n\n    // 根据类别筛选\n    async onCategoryChange(e) {\n      this.categoryIndex = e.detail.value;\n      // 分类筛选时清空搜索关键词\n      this.searchKeyword = '';\n      \n      if (this.categoryIndex > 0) {\n        const selectedCategory = this.categories[this.categoryIndex];\n        this.loading = true;\n        try {\n          const response = await foodApi.getFoodByCategory({\n            category: selectedCategory\n          });\n          \n          if (response.code === 200) {\n            this.foods = response.data || [];\n          }\n        } catch (error) {\n          console.error('获取类别数据失败:', error);\n        } finally {\n          this.loading = false;\n        }\n      } else {\n        this.loadFoods();\n      }\n    },\n\n    onSortChange(e) {\n      this.sortIndex = e.detail.value;\n    },\n\n    setFilter(filter) {\n      this.currentFilter = filter;\n    },\n\n    // 获取升糖指数等级\n    getGiLevel(gi) {\n      if (!gi) return '暂无';\n      if (gi < 55) return '低';\n      if (gi < 70) return '中';\n      return '高';\n    },\n\n    // 获取升糖负荷等级\n    getGlLevel(gl) {\n      if (!gl) return '暂无';\n      if (gl < 10) return '低';\n      if (gl < 20) return '中';\n      return '高';\n    },\n\n    // 获取食物图片\n    getFoodImage(foodName) {\n      // 这里可以根据食物名称返回对应的图片路径\n      // 如果没有图片，返回默认图片\n      const imageMap = {\n        '苹果': '/static/foods/apple.jpg',\n        '香蕉': '/static/foods/banana.jpg',\n        '西瓜': '/static/foods/watermelon.jpg',\n        '白米饭': '/static/foods/rice.jpg',\n        '燕麦': '/static/foods/oats.jpg',\n        '全麦面包': '/static/foods/wheat-bread.jpg',\n        '红薯': '/static/foods/sweet-potato.jpg',\n        '胡萝卜': '/static/foods/carrot.jpg',\n        '西兰花': '/static/foods/broccoli.jpg',\n        '鸡胸肉': '/static/foods/chicken-breast.jpg',\n        '核桃': '/static/foods/walnut.jpg',\n        '杏仁': '/static/foods/almond.jpg'\n      };\n      \n      return imageMap[foodName] || '/static/foods/default.jpg';\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* 顶部绿色背景区域 */\n.header-section {\n  background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);\n  padding: 20rpx 30rpx 60rpx;\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 6rpx 24rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);\n  border-radius: 0 0 32rpx 32rpx;\n  z-index: 2;\n}\n\n.header-section::after {\n  content: '';\n  position: absolute;\n  bottom: -20rpx;\n  right: -50rpx;\n  width: 200rpx;\n  height: 200rpx;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n}\n\n.wechat-group-banner {\n  text-align: center;\n  color: white;\n}\n\n.banner-title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.banner-subtitle {\n  display: block;\n  font-size: 28rpx;\n  opacity: 0.9;\n}\n\n/* 搜索区域 */\n.search-section {\n  padding: 30rpx;\n  background: white;\n  margin-top: -30rpx;\n  border-radius: 20rpx 20rpx 0 0;\n  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.10), 0 1rpx 2rpx rgba(0,0,0,0.04);\n  z-index: 3;\n}\n\n.search-bar {\n  display: flex;\n  align-items: center;\n  gap: 20rpx;\n}\n\n.category-picker {\n  display: flex;\n  align-items: center;\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 10rpx;\n  min-width: 160rpx;\n  font-size: 28rpx;\n}\n\n.picker-arrow {\n  margin-left: 10rpx;\n  font-size: 24rpx;\n  color: #666;\n}\n\n.search-input {\n  flex: 1;\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n}\n\n.search-btn {\n  background: #4CAF50;\n  color: white;\n  border: none;\n  padding: 20rpx 30rpx;\n  border-radius: 10rpx;\n  font-size: 28rpx;\n  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);\n  transition: box-shadow 0.2s, transform 0.2s;\n}\n.search-btn:active {\n  box-shadow: 0 2rpx 4rpx rgba(76, 175, 80, 0.10);\n  transform: translateY(2rpx) scale(0.98);\n}\n\n/* 标题区域 */\n.title-section {\n  padding: 40rpx 30rpx;\n  text-align: center;\n  background: white;\n}\n\n.main-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #4CAF50;\n}\n\n/* 分类筛选 */\n.filter-section {\n  display: flex;\n  justify-content: space-between;\n  gap: 15rpx;\n  padding: 40rpx 30rpx;\n  background: white;\n  border-bottom: 1rpx solid #eee;\n}\n\n.filter-item {\n  flex: 1;\n  padding: 20rpx 10rpx;\n  background: #f8f8f8;\n  border-radius: 30rpx;\n  font-size: 26rpx;\n  color: #666;\n  text-align: center;\n  transition: all 0.3s;\n  border: 2rpx solid transparent;\n  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);\n  position: relative;\n}\n\n.filter-item.active {\n  background: #4CAF50;\n  color: white;\n  border-color: #4CAF50;\n  box-shadow: 0 6rpx 18rpx rgba(76, 175, 80, 0.18), 0 1.5rpx 4rpx rgba(0,0,0,0.04);\n}\n\n/* 排序选择 */\n.sort-section {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  background: white;\n  border-bottom: 1rpx solid #eee;\n}\n\n.sort-label {\n  font-size: 28rpx;\n  color: #666;\n  margin-right: 20rpx;\n}\n\n.sort-picker {\n  display: flex;\n  align-items: center;\n  font-size: 28rpx;\n  color: #333;\n}\n\n/* 加载状态 */\n.loading-section {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 100rpx 0;\n  background: white;\n  margin: 20rpx 30rpx;\n  border-radius: 15rpx;\n}\n\n.loading-section text {\n  font-size: 28rpx;\n  color: #666;\n}\n\n/* 无数据提示 */\n.no-data {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 100rpx 0;\n  background: white;\n  margin: 20rpx 30rpx;\n  border-radius: 15rpx;\n}\n\n.no-data text {\n  font-size: 28rpx;\n  color: #999;\n}\n\n/* 食物列表 */\n.food-list {\n  padding: 0 30rpx;\n}\n\n.food-item {\n  display: flex;\n  align-items: center;\n  padding: 30rpx;\n  background: white;\n  border-radius: 15rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.13), 0 2rpx 8rpx rgba(0,0,0,0.06);\n  transition: box-shadow 0.2s, transform 0.2s;\n  position: relative;\n  z-index: 1;\n}\n.food-item:active {\n  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.10);\n  transform: translateY(2rpx) scale(0.98);\n}\n\n.food-image {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 10rpx;\n  margin-right: 30rpx;\n  background: #f0f0f0;\n}\n\n.food-info {\n  flex: 1;\n}\n\n.food-name {\n  display: block;\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 15rpx;\n}\n\n.food-details {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.gi-value {\n  font-size: 26rpx;\n  color: #4CAF50;\n  font-weight: 500;\n}\n\n.gl-value {\n  font-size: 26rpx;\n  color: #FF9800;\n  font-weight: 500;\n}\n\n.calorie-value {\n  font-size: 26rpx;\n  color: #666;\n  font-weight: 500;\n}\n\n/* 食物列表底部间距 */\n.food-list {\n  padding-bottom: 40rpx;\n}\n</style>", "import mod from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\吉安数据11到12\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753972578521\n      var cssReload = require(\"E:/吉安数据11到12/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}