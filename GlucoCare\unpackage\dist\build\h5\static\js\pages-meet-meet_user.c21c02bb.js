(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-meet-meet_user"],{"0ce5":function(t,e,i){"use strict";i.r(e);var n=i("0e27"),s=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=s.a},"0e27":function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a434"),i("c740"),i("14d9"),i("fb6a");var s=n(i("e67e")),a=n(i("1ad5")),o={data:function(){return{listShow:!1,keyword:"",deptList:[],userList:[],topViewList:[],ifBorder:!1,choseUserList:[],choseDeptList:[]}},onLoad:function(t){this.getTopDeptList()},onShow:function(t){var e=uni.getStorageSync("meetInfo");this.choseUserList=e.choseUserList,this.choseDeptList=e.choseDeptList},methods:{addMeetPeople:function(){var t=this.choseUserList,e=this.choseDeptList,i=uni.getStorageSync("meetInfo");i.choseUserList=t,i.choseDeptList=e,uni.setStorageSync("meetInfo",i),uni.navigateTo({url:"/pages/meet/meet_reservation_submit"})},deleteUser:function(t,e){var i=this.choseUserList;i.splice(e,1),this.choseUserList=i;var n=this.userList,s=n.findIndex((function(e){return e.userId===t.userId}));s>-1&&this.$set(this.userList[s],"isChecked",!1)},deleteDept:function(t,e){var i=this.choseDeptList;i.splice(e,1),this.choseDeptList=i;var n=this.deptList,s=n.findIndex((function(e){return e.deptId===t.deptId}));s>-1&&this.$set(this.deptList[s],"isChecked",!1)},getChoseList:function(){this.listShow=!0},closeListShow:function(){this.listShow=!1},choseDept:function(t,e){var i=this.choseDeptList,n=this,s=this.deptList[e].isChecked;s?uni.showModal({title:"提示",content:"是否取消选中当前部门下的人员",success:function(s){if(s.confirm){n.$set(n.deptList[e],"isChecked",!1);var a=i.findIndex((function(e){return e.deptId===t.deptId}));a>-1&&(i.splice(a,1),n.choseDeptList=i)}else s.cancel&&console.log("用户点击取消")}}):(n.$set(n.deptList[e],"isChecked",!0),n.choseDeptList.push(t))},choseUser:function(t,e){var i=this.choseUserList,n=this,s=this.userList[e].isChecked;s?uni.showModal({title:"提示",content:"是否取消选中当前人员",success:function(s){if(s.confirm){n.$set(n.userList[e],"isChecked",!1);var a=i.findIndex((function(e){return e.userId===t.userId}));a>-1&&(i.splice(a,1),n.choseUserList=i)}else s.cancel&&console.log("用户点击取消")}}):(n.$set(n.userList[e],"isChecked",!0),n.choseUserList.push(t))},searchUsers:function(){var t=this,e=this.keyword;a.default.getUserListByKeyword({keyword:e}).then((function(e){uni.hideLoading(),200==e.code&&(t.deptList=[],t.topViewList=[],t.userList=e.data)}))},cancelSearch:function(){this.keyword=null,this.userList=[],this.topViewList=[],this.getTopDeptList()},getTopDeptList:function(){var t=this;s.default.getTopDept().then((function(e){200==e.code&&(t.deptList=e.data)}))},getChildenDepts:function(t){var e=this,i=this.choseDeptList,n=this.choseUserList;if(!t.isChecked||!t.checked){var a=t.deptId;s.default.getChildenDept({parentId:a,choseDeptList:i,choseUserList:n}).then((function(i){200==i.code&&(e.topViewList.push(t),e.deptList=i.data.depts,e.userList=i.data.users)}))}},clickTopDept:function(){this.getTopDeptList(),this.topViewList=[]},clickTopDeptList:function(t){var e=this,i=this.topViewList[t];this.topViewList=this.topViewList.slice(0,t+1);var n=i.deptId;s.default.getChildenDept({parentId:n}).then((function(t){200==t.code&&(e.deptList=t.data.depts,e.userList=t.data.users)}))}}};e.default=o},"1ad5":function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=n(i("3d6e")),a={getUserListByKeyword:function(t){return(0,s.default)({url:"/app/user/getUserListByKeyword",method:"get",params:t})},getUserInfo:function(t){return(0,s.default)({url:"/app/user/getUserInfo",method:"get",params:t})}};e.default=a},"27e2":function(t,e,i){var n=i("8ca1");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var s=i("4f06").default;s("74983a2e",n,!0,{sourceMap:!1,shadowMode:!1})},"2a8c":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-58a271ec]{height:100%;width:100%;background-color:#f3f4f6}body.?%PAGE?%[data-v-58a271ec]{background-color:#f3f4f6}.all-view[data-v-58a271ec]{width:100%;height:100%}.top-search[data-v-58a271ec]{padding:%?20?%}.view-title[data-v-58a271ec]{box-sizing:border-box;width:100%;padding-left:%?20?%;padding-right:%?20?%;padding-top:%?20?%;padding-bottom:%?10?%;display:flex;flex-direction:row;font-size:%?30?%;background-color:#fff;overflow-x:auto;white-space:nowrap;display:flex}.organ-list-view[data-v-58a271ec]{background-color:#fff;height:80%}.cell-hover-class[data-v-58a271ec]{background-color:#4871c0}.button-view[data-v-58a271ec]{position:absolute;bottom:0;height:%?200?%;width:100%;display:flex;flex-direction:column;align-items:center;background-color:#fff}.button-view-title[data-v-58a271ec]{width:100%;height:%?70?%;background-color:#f3f4f6;text-align:left;line-height:%?70?%;display:flex;flex-direction:column;align-items:center;border-top-left-radius:%?15?%;border-top-right-radius:%?15?%}.button-view-title-text[data-v-58a271ec]{color:#6f7072;font-size:%?30?%;width:95%;border-bottom:%?3?% solid #dee0e3}.button-view-content[data-v-58a271ec]{width:100%;height:%?130?%;background-color:#f3f4f6;display:flex;flex-direction:column;align-items:center}.button-view-content-view[data-v-58a271ec]{color:#6f7072;width:95%;border-top:%?3?% solid #dee0e3;display:flex;flex-direction:row;align-items:center;justify-content:space-between}.user-list[data-v-58a271ec]{width:75%;height:%?100?%;display:flex;flex-direction:row;align-items:center;overflow-x:auto}.center-button[data-v-58a271ec]{margin-top:%?20?%;width:20%;background-color:#427ce8;text-align:center;height:%?60?%;line-height:%?60?%;color:#fff;border-radius:%?10?%;font-size:%?20?%}.list-show-view[data-v-58a271ec]{width:100%;height:%?1000?%;background-color:#fff;border-radius:%?50?%}.list-show-view-title[data-v-58a271ec]{height:%?100?%;display:flex;flex-direction:row;margin-top:%?20?%;justify-content:space-between}.list-show-view-content[data-v-58a271ec]{width:100%;height:%?850?%;display:flex;flex-direction:column;align-items:center;overflow-y:auto}.list-show-view-content-item[data-v-58a271ec]{height:%?80?%;width:90%;display:flex;flex-direction:row;justify-content:space-between;margin-bottom:%?20?%;align-items:center}.list-show-view-content-item-left[data-v-58a271ec]{display:flex;flex-direction:row;align-items:center}.list-show-view-content-item-right[data-v-58a271ec]{width:%?80?%;height:%?40?%;line-height:%?40?%;text-align:center;border-radius:%?10?%;border:1px solid #5c5c5c;font-size:%?25?%}',""]),t.exports=e},3900:function(t,e,i){"use strict";i.r(e);var n=i("74cd"),s=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=s.a},"3a8a":function(t,e,i){"use strict";i.r(e);var n=i("f046"),s=i("0ce5");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);i("961a");var o=i("f0c5"),r=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"58a271ec",null,!1,n["a"],void 0);e["default"]=r.exports},"5b8f":function(t,e,i){"use strict";var n=i("27e2"),s=i.n(n);s.a},"74cd":function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=n(i("838c")),a={name:"u-search",mixins:[uni.$u.mpMixin,uni.$u.mixin,s.default],data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!this.animation&&this.showAction}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")},clickIcon:function(){this.$emit("clickIcon")}}};e.default=a},"838c":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("ac1f"),i("841c"),i("a9e3");var n={props:{shape:{type:String,default:uni.$u.props.search.shape},bgColor:{type:String,default:uni.$u.props.search.bgColor},placeholder:{type:String,default:uni.$u.props.search.placeholder},clearabled:{type:Boolean,default:uni.$u.props.search.clearabled},focus:{type:Boolean,default:uni.$u.props.search.focus},showAction:{type:Boolean,default:uni.$u.props.search.showAction},actionStyle:{type:Object,default:uni.$u.props.search.actionStyle},actionText:{type:String,default:uni.$u.props.search.actionText},inputAlign:{type:String,default:uni.$u.props.search.inputAlign},inputStyle:{type:Object,default:uni.$u.props.search.inputStyle},disabled:{type:Boolean,default:uni.$u.props.search.disabled},borderColor:{type:String,default:uni.$u.props.search.borderColor},searchIconColor:{type:String,default:uni.$u.props.search.searchIconColor},color:{type:String,default:uni.$u.props.search.color},placeholderColor:{type:String,default:uni.$u.props.search.placeholderColor},searchIcon:{type:String,default:uni.$u.props.search.searchIcon},searchIconSize:{type:[Number,String],default:uni.$u.props.search.searchIconSize},margin:{type:String,default:uni.$u.props.search.margin},animation:{type:Boolean,default:uni.$u.props.search.animation},value:{type:String,default:uni.$u.props.search.value},maxlength:{type:[String,Number],default:uni.$u.props.search.maxlength},height:{type:[String,Number],default:uni.$u.props.search.height},label:{type:[String,Number,null],default:uni.$u.props.search.label}}};e.default=n},"8ca1":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-view[data-v-313cf2d0], uni-scroll-view[data-v-313cf2d0], uni-swiper-item[data-v-313cf2d0]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}[type="search"][data-v-313cf2d0]::-webkit-search-decoration{display:none}.u-search[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center;flex:1}.u-search__content[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center;padding:0 10px;flex:1;justify-content:space-between;border-width:1px;border-color:transparent;border-style:solid;overflow:hidden}.u-search__content__icon[data-v-313cf2d0]{display:flex;flex-direction:row;align-items:center}.u-search__content__label[data-v-313cf2d0]{color:#303133;font-size:14px;margin:0 4px}.u-search__content__close[data-v-313cf2d0]{width:20px;height:20px;border-top-left-radius:100px;border-top-right-radius:100px;border-bottom-left-radius:100px;border-bottom-right-radius:100px;background-color:#c6c7cb;display:flex;flex-direction:row;align-items:center;justify-content:center;-webkit-transform:scale(.82);transform:scale(.82)}.u-search__content__input[data-v-313cf2d0]{flex:1;font-size:14px;line-height:1;margin:0 5px;color:#303133}.u-search__content__input--placeholder[data-v-313cf2d0]{color:#909193}.u-search__action[data-v-313cf2d0]{font-size:14px;color:#303133;width:0;overflow:hidden;transition-property:width;transition-duration:.3s;white-space:nowrap;text-align:center}.u-search__action--active[data-v-313cf2d0]{width:40px;margin-left:5px}',""]),t.exports=e},"90a0":function(t,e,i){var n=i("2a8c");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var s=i("4f06").default;s("185324f8",n,!0,{sourceMap:!1,shadowMode:!1})},"961a":function(t,e,i){"use strict";var n=i("90a0"),s=i.n(n);s.a},e0a6:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uIcon:i("3ccb").default},s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-search",style:[{margin:t.margin},t.$u.addStyle(t.customStyle)],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-search__content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100px":"4px",borderColor:t.borderColor}},[t.$slots.label||null!==t.label?[t._t("label",[i("v-uni-text",{staticClass:"u-search__content__label"},[t._v(t._s(t.label))])])]:t._e(),i("v-uni-view",{staticClass:"u-search__content__icon"},[i("u-icon",{attrs:{size:t.searchIconSize,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickIcon.apply(void 0,arguments)}}})],1),i("v-uni-input",{staticClass:"u-search__content__input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor,height:t.$u.addUnit(t.height)},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-search__content__input--placeholder",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?i("v-uni-view",{staticClass:"u-search__content__icon u-search__content__close",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[i("u-icon",{attrs:{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}})],1):t._e()],2),i("v-uni-text",{staticClass:"u-search__action",class:[(t.showActionBtn||t.show)&&"u-search__action--active"],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))])],1)},a=[]},e67e:function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=n(i("3d6e")),a={getTopDept:function(){return(0,s.default)({url:"/app/dept/getTopDept",method:"get"})},getChildenDept:function(t){return(0,s.default)({url:"/app/dept/getChildenDept",method:"post",data:t})}};e.default=a},f046:function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uSearch:i("f473").default,uList:i("f1af").default,uListItem:i("bd2a").default,uCell:i("d8f1").default,uAvatar:i("4e72").default,uPopup:i("4da1").default},s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"all-view"},[t.keyword?i("v-uni-view",{staticClass:"top-search"},[i("u-search",{attrs:{shape:"square",placeholder:"请输入手机号或姓名",inputAlign:"center",bgColor:"#ffffff",actionText:"取消",animation:!1},on:{search:function(e){arguments[0]=e=t.$handleEvent(e),t.searchUsers.apply(void 0,arguments)},custom:function(e){arguments[0]=e=t.$handleEvent(e),t.cancelSearch.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1):i("v-uni-view",{staticClass:"top-search"},[i("u-search",{attrs:{shape:"square",showAction:!1,placeholder:"请输入手机号或姓名",inputAlign:"center",bgColor:"#ffffff"},on:{search:function(e){arguments[0]=e=t.$handleEvent(e),t.searchUsers.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),i("v-uni-view",{staticClass:"view-title"},[i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTopDept(t.index)}}},[t._v("企业通讯录")]),t._l(t.topViewList,(function(e,n){return i("v-uni-view",{key:n,staticStyle:{color:"#4871c0"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickTopDeptList(n)}}},[t._v("> "+t._s(e.deptName))])})),i("v-uni-view",{staticStyle:{width:"50rpx"}})],2),i("v-uni-view",{staticClass:"organ-list-view"},[i("u-list",{attrs:{height:"100%"}},[t._l(t.userList,(function(e,n){return i("u-list-item",{key:n},[i("v-uni-view",{staticStyle:{display:"flex","flex-direction":"row","align-items":"center",width:"100%"}},[i("v-uni-view",{on:{click:function(i){arguments[0]=i=t.$handleEvent(i),function(){return t.choseUser(e,n)}.apply(void 0,arguments)}}},[i("v-uni-radio",{staticStyle:{"margin-left":"10rpx",width:"50rpx"},attrs:{checked:e.isChecked||e.checked,value:e.id}})],1),i("u-cell",{staticStyle:{width:"calc(100% - 60rpx)"},attrs:{title:e.userName,"arrow-direction":"right",size:"large"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.choseUser(e,n)}}},[i("v-uni-view",{staticStyle:{"font-size":"20rpx","margin-top":"2rpx",color:"#909193"},attrs:{slot:"label"},slot:"label"},[t._v(t._s(e.deptChains))]),i("u-avatar",{attrs:{slot:"icon",shape:"square",size:"35",src:"../../static/communication/user.png",customStyle:"margin: -3px 5px -3px 0;"},slot:"icon"})],1)],1)],1)})),t._l(t.deptList,(function(e,n){return i("u-list-item",{key:n},[i("v-uni-view",{staticStyle:{display:"flex","flex-direction":"row","align-items":"center",width:"100%"}},[i("v-uni-view",{on:{click:function(i){arguments[0]=i=t.$handleEvent(i),function(){return t.choseDept(e,n)}.apply(void 0,arguments)}}},[i("v-uni-radio",{staticStyle:{"margin-left":"10rpx",width:"50rpx"},attrs:{checked:e.isChecked||e.checked,value:e.id}})],1),i("u-cell",{staticStyle:{width:"calc(100% - 60rpx)"},attrs:{title:e.deptName+"("+e.employeeCount+")",isLink:!0,"arrow-direction":"right",size:"large"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.getChildenDepts(e)}}},[i("u-avatar",{attrs:{slot:"icon",shape:"square",size:"35",src:"../../static/communication/dept.png",customStyle:"margin: -3px 5px -3px -10rpx;"},slot:"icon"})],1)],1)],1)})),i("v-uni-view",{staticStyle:{height:"400rpx"}})],2)],1),i("v-uni-view",{staticClass:"button-view"},[i("v-uni-view",{staticClass:"button-view-title",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getChoseList.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"button-view-title-text"},[t._v("请选择参会人员")])],1),i("v-uni-view",{staticClass:"button-view-content"},[i("v-uni-view",{staticClass:"button-view-content-view"},[i("v-uni-view",{staticClass:"user-list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.getChoseList.apply(void 0,arguments)}}},[t._l(t.choseDeptList,(function(t,e){return i("u-avatar",{key:e,attrs:{shape:"square",size:"35",src:"../../static/communication/dept.png",customStyle:"margin-right: 5rpx;"}})})),t._l(t.choseUserList,(function(t,e){return i("u-avatar",{key:e,attrs:{text:t.userName.charAt(0),size:"35",customStyle:"margin-right: 5rpx;"}})}))],2),i("v-uni-view",{staticClass:"center-button",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.addMeetPeople.apply(void 0,arguments)}}},[t._v("确定")])],1)],1)],1),i("u-popup",{attrs:{show:t.listShow,round:10},on:{close:function(e){arguments[0]=e=t.$handleEvent(e),t.closeListShow.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"list-show-view"},[i("v-uni-view",{staticClass:"list-show-view-title"},[i("v-uni-view",{staticStyle:{width:"120rpx","text-align":"center"}}),i("v-uni-view",{staticStyle:{width:"120rpx","text-align":"center"}},[t._v("已选择")]),i("v-uni-view",{staticStyle:{width:"120rpx","text-align":"center",color:"#5e87ed","font-size":"30rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeListShow.apply(void 0,arguments)}}},[t._v("确定")])],1),i("v-uni-view",{staticClass:"list-show-view-content"},[t._l(t.choseUserList,(function(e,n){return i("v-uni-view",{key:e.userId,staticClass:"list-show-view-content-item"},[i("v-uni-view",{staticClass:"list-show-view-content-item-left"},[i("u-avatar",{attrs:{shape:"square",size:"35",src:"../../static/communication/user.png"}}),i("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.userName))])],1),i("v-uni-view",{staticClass:"list-show-view-content-item-right",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteUser(e,n)}}},[t._v("移除")])],1)})),t._l(t.choseDeptList,(function(e,n){return i("v-uni-view",{key:e.deptId,staticClass:"list-show-view-content-item"},[i("v-uni-view",{staticClass:"list-show-view-content-item-left"},[i("u-avatar",{attrs:{shape:"square",size:"35",src:"../../static/communication/dept.png"}}),i("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.deptName))])],1),i("v-uni-view",{staticClass:"list-show-view-content-item-right",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.deleteDept(e,n)}}},[t._v("移除")])],1)}))],2)],1)],1)],1)},a=[]},f473:function(t,e,i){"use strict";i.r(e);var n=i("e0a6"),s=i("3900");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(a);i("5b8f");var o=i("f0c5"),r=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"313cf2d0",null,!1,n["a"],void 0);e["default"]=r.exports}}]);